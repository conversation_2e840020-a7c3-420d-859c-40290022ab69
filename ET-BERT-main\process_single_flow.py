#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
单个流处理脚本
将单个pcap流文件转换为TSV格式的特征数据
参照 process_minority_classes.py 的特征提取逻辑
"""

import os
import sys
import json
import csv
import subprocess
import tempfile
import argparse

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data_process'))

# 导入数据处理模块
try:
    from data_process.dataset_generation import generation, bigram_generation
except ImportError:
    import data_process.dataset_generation as dataset_generation
    generation = dataset_generation.generation
    bigram_generation = dataset_generation.bigram_generation

# 尝试导入scapy作为备用方案
try:
    import scapy.all as scapy
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False





def extract_payload_with_tshark(pcap_file, max_packets=5, timeout=30, debug=False):
    """使用tshark提取payload数据（标准特征）"""
    try:
        cmd = [
            'tshark', '-r', pcap_file, '-T', 'fields', '-e', 'data',
            '-Y', 'data', '-c', str(max_packets)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)

        if debug:
            print(f"    tshark返回码: {result.returncode}")
            print(f"    tshark输出长度: {len(result.stdout) if result.stdout else 0}")
            if result.stderr:
                print(f"    tshark错误: {result.stderr[:100]}")

        if result.returncode == 0 and result.stdout.strip():
            hex_data = result.stdout.strip().replace('\n', '').replace(':', '')
            if debug:
                print(f"    提取的hex数据长度: {len(hex_data)}")

            if len(hex_data) >= 20:  # 至少10个字节
                bigram_result = bigram_generation(hex_data)
                if debug:
                    print(f"    bigram生成结果长度: {len(bigram_result) if bigram_result else 0}")
                return bigram_result
            elif debug:
                print(f"    hex数据太短: {len(hex_data)} < 20")
        elif debug:
            print(f"    tshark失败或无输出")

        return None

    except Exception as e:
        if debug:
            print(f"    tshark异常: {e}")
        return None



def extract_payload_with_scapy(pcap_file, max_packets=5):
    """使用scapy提取payload数据（标准特征，备用方案）"""
    try:
        packets = scapy.rdpcap(pcap_file)
        hex_data = ""

        count = 0
        for packet in packets:
            if count >= max_packets:
                break
            if packet.haslayer(scapy.Raw):
                hex_data += packet[scapy.Raw].load.hex()
                count += 1

        if len(hex_data) >= 20:
            return bigram_generation(hex_data)
        else:
            return None

    except Exception as e:
        return None

def process_pcap_to_tsv(pcap_file_path, label, output_file_path=None, debug=False):
    """
    处理单个pcap文件并生成TSV格式的特征数据

    Args:
        pcap_file_path (str): 输入pcap文件的路径
        label (int): 流的标签（0-13）
        output_file_path (str, optional): 输出TSV文件路径，默认为输入文件名.tsv
        debug (bool, optional): 是否启用调试输出，默认False

    Returns:
        dict: 包含处理结果的字典，格式为 {'success': bool, 'message': str, 'output_file': str}
    """
    try:
        # 验证输入参数
        if not isinstance(pcap_file_path, str) or not pcap_file_path.strip():
            return {
                'success': False,
                'message': '错误：pcap文件路径不能为空',
                'output_file': None
            }

        if not isinstance(label, int) or label < 0 or label > 13:
            return {
                'success': False,
                'message': '错误：标签必须是0-13之间的整数',
                'output_file': None
            }

        # 检查文件是否存在
        if not os.path.exists(pcap_file_path):
            return {
                'success': False,
                'message': f'错误：文件不存在: {pcap_file_path}',
                'output_file': None
            }

        # 检查文件大小
        file_size = os.path.getsize(pcap_file_path)
        if file_size == 0:
            return {
                'success': False,
                'message': f'错误：空文件: {pcap_file_path}',
                'output_file': None
            }
        if file_size < 100:
            return {
                'success': False,
                'message': f'错误：文件过小: {pcap_file_path} ({file_size} bytes)',
                'output_file': None
            }

        # 确定输出文件路径
        if output_file_path is None:
            base_name = os.path.splitext(pcap_file_path)[0]
            output_file_path = f"{base_name}.tsv"

        if debug:
            print(f"处理文件: {os.path.basename(pcap_file_path)} ({file_size} bytes)")
            print("  使用标准特征提取（payload bigram特征）")

        # 首先尝试tshark标准提取
        payload_data = extract_payload_with_tshark(pcap_file_path, max_packets=5, timeout=30, debug=debug)

        # 如果tshark失败，尝试scapy备用方案
        if not payload_data and SCAPY_AVAILABLE:
            if debug:
                print("  tshark失败，尝试scapy特征提取")
            payload_data = extract_payload_with_scapy(pcap_file_path, max_packets=5)

        if not payload_data:
            return {
                'success': False,
                'message': '错误：特征提取失败，无法从pcap文件中提取有效的payload数据',
                'output_file': None
            }

        if debug:
            print(f"  ✓ 特征提取成功，长度: {len(payload_data)}")

        # 写入TSV文件
        try:
            with open(output_file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter='\t')
                writer.writerow(["label", "text_a"])  # 写入头部
                writer.writerow([label, payload_data])  # 写入数据

            # 验证文件是否成功创建
            if not os.path.exists(output_file_path):
                return {
                    'success': False,
                    'message': f'错误：TSV文件创建失败: {output_file_path}',
                    'output_file': None
                }

            output_file_size = os.path.getsize(output_file_path)

            return {
                'success': True,
                'message': f'成功生成TSV文件: {output_file_path} ({output_file_size} bytes, 特征长度: {len(payload_data)})',
                'output_file': output_file_path
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'错误：写入TSV文件失败: {str(e)}',
                'output_file': None
            }

    except Exception as e:
        return {
            'success': False,
            'message': f'错误：处理异常: {str(e)}',
            'output_file': None
        }



def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='处理单个pcap流文件并转换为TSV格式')
    parser.add_argument('pcap_file', help='输入的pcap文件路径')
    parser.add_argument('--label', type=int, required=True, help='流的标签（0-13）')
    parser.add_argument('--output', '-o', help='输出TSV文件路径（可选，默认为输入文件名.tsv）')
    parser.add_argument('--debug', action='store_true', help='启用调试输出')

    args = parser.parse_args()

    print("=" * 60)
    print("单个流处理脚本")
    print("=" * 60)
    print(f"输入文件: {args.pcap_file}")
    print(f"标签: {args.label}")
    print(f"输出文件: {args.output if args.output else '自动生成'}")
    print(f"特征类型: payload bigram特征")
    print(f"调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 60)

    # 调用新的处理函数
    result = process_pcap_to_tsv(
        pcap_file_path=args.pcap_file,
        label=args.label,
        output_file_path=args.output,
        debug=args.debug
    )

    # 处理结果
    if result['success']:
        print(f"\n✓ {result['message']}")
        return 0
    else:
        print(f"\n✗ {result['message']}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

##使用方法
# 基本用法
#python process_single_flow.py flow.pcap --label 0

# 指定输出文件
#python process_single_flow.py flow.pcap --label 7 --output my_output.tsv

# 启用调试模式
#python process_single_flow.py flow.pcap --label 0 --debug