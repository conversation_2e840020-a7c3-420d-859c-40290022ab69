#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
VPN数据集分批处理脚本 - 流级别并行版本
- 文件级别并行：使用60%的CPU核心
- 流级别并行：使用80%的CPU核心
- 内存友好：大文件分批处理，立即写入TSV
"""

import os
import sys
import json
import random
import csv
import gc
import time
import subprocess
import tempfile
import shutil
import multiprocessing
import psutil  # 用于内存监控
import concurrent.futures
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
from queue import Queue
from sklearn.model_selection import train_test_split

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data_process'))

# 导入数据处理模块
try:
    from data_process.dataset_generation import generation, bigram_generation
except ImportError:
    import data_process.dataset_generation as dataset_generation
    generation = dataset_generation.generation
    bigram_generation = dataset_generation.bigram_generation

import subprocess
import binascii

# 尝试导入scapy作为备用方案
try:
    import scapy.all as scapy
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    print("警告: scapy不可用，无法使用备用提取方法")

# VPN类别映射
VPN_CATEGORIES = [
    "OpenVPN+Vless", "OpenVPN+Vmess", "SS+Vmess", "SSLVPN", "SSR+Vmess",
    "Vless", "Vmess", "Wireguard+SSH", "Wireguard+SSR", "ciscovpn",
    "clash_doh", "clash_dot", "ipsec", "l2tp", "openvpn", "wireguard"
]

def create_label_mapping():
    """创建VPN类别到数字标签的映射"""
    label_mapping = {}
    reverse_mapping = {}

    for idx, category in enumerate(VPN_CATEGORIES):
        label_mapping[category] = idx
        reverse_mapping[idx] = category

    return label_mapping, reverse_mapping

def extract_payload_with_tshark(pcap_file, max_packets=5):
    """
    使用tshark快速提取pcap文件的payload数据，支持加密文件
    """
    try:
        # 方法1: 尝试提取应用层数据，按优先级排序
        methods = [
            # 提取TCP payload（最常用）
            {
                'cmd': [
                    'tshark', '-r', pcap_file,
                    '-T', 'fields',
                    '-e', 'tcp.payload',
                    '-c', str(max_packets),
                    '-Y', 'tcp and tcp.payload'  # 只要有TCP payload就行
                ],
                'name': 'TCP payload提取'
            },
            # 提取UDP payload
            {
                'cmd': [
                    'tshark', '-r', pcap_file,
                    '-T', 'fields',
                    '-e', 'udp.payload',
                    '-c', str(max_packets),
                    '-Y', 'udp and udp.payload'  # 只要有UDP payload就行
                ],
                'name': 'UDP payload提取'
            },
            # 提取原始数据
            {
                'cmd': [
                    'tshark', '-r', pcap_file,
                    '-T', 'fields',
                    '-e', 'data.data',
                    '-c', str(max_packets),
                    '-Y', 'data.data'  # 只要有数据就行
                ],
                'name': '原始数据提取'
            },
            # 提取完整帧数据（包括头部）- 最后的备用方案
            {
                'cmd': [
                    'tshark', '-r', pcap_file,
                    '-T', 'fields',
                    '-e', 'frame.raw',
                    '-c', str(max_packets)
                    # 不加过滤条件，获取所有帧
                ],
                'name': '完整帧数据提取'
            }
        ]

        for method in methods:
            try:
                result = subprocess.run(method['cmd'], capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    payload_data = ""
                    valid_lines = 0

                    for line in lines:
                        line = line.strip()
                        if line:  # 有数据
                            # 移除冒号和空格，转换为连续的hex字符串
                            hex_data = line.replace(':', '').replace(' ', '')
                            if hex_data and len(hex_data) > 20:  # 确保有足够的数据
                                # 生成bigram特征
                                payload_data += bigram_generation(hex_data, packet_len=128, flag=True)
                                valid_lines += 1

                    if payload_data and valid_lines > 0:
                        return payload_data[:200000]  # 限制长度

            except subprocess.TimeoutExpired:
                continue
            except Exception:
                continue

        return None

    except Exception as e:
        return None

def extract_payload_with_scapy(pcap_file, max_packets=5):
    """
    使用scapy作为备用方法提取pcap文件的payload数据
    """
    if not SCAPY_AVAILABLE:
        return None

    try:
        packets = scapy.rdpcap(pcap_file)

        if len(packets) == 0:
            return None

        payload_data = ""
        processed_packets = 0

        for packet in packets[:max_packets]:
            if processed_packets >= max_packets:
                break

            try:
                # 获取原始包数据
                packet_data = packet.copy()
                data = binascii.hexlify(bytes(packet_data))
                packet_string = data.decode()

                # 跳过头部，从payload开始（大约76个hex字符后）
                if len(packet_string) > 76:
                    new_packet_string = packet_string[76:]
                    payload_data += bigram_generation(new_packet_string, packet_len=128, flag=True)
                    processed_packets += 1

            except Exception:
                continue

        if payload_data:
            return payload_data[:200000]  # 限制长度
        else:
            return None

    except Exception as e:
        return None

def split_pcap_into_flows(pcap_path, output_dir):
    """
    使用SplitCap将pcap文件分割为流
    """
    try:
        # 确保使用绝对路径
        pcap_path = os.path.abspath(pcap_path)
        output_dir = os.path.abspath(output_dir)

        # 检查输入文件是否存在
        if not os.path.exists(pcap_path):
            print(f"    输入文件不存在: {pcap_path}")
            return []

        # 检查文件大小，对超大文件给出警告但仍处理
        file_size_mb = os.path.getsize(pcap_path) / (1024 * 1024)
        if file_size_mb > 2000:  # 超过2GB的文件给出警告
            print(f"    警告：处理大文件 {os.path.basename(pcap_path)} ({file_size_mb:.1f}MB)，可能需要较长时间")
        elif file_size_mb > 5000:  # 超过5GB的文件才跳过
            print(f"    跳过超大文件: {os.path.basename(pcap_path)} ({file_size_mb:.1f}MB)")
            return []

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # SplitCap命令：按会话分割
        splitcap_exe = os.path.join(os.path.dirname(__file__), 'SplitCap.exe')
        if not os.path.exists(splitcap_exe):
            # 备用路径
            splitcap_exe = "SplitCap.exe"

        cmd = f'"{splitcap_exe}" -r "{pcap_path}" -s session -o "{output_dir}"'

        # 调试信息
        print(f"    SplitCap命令: {cmd}")

        # 根据文件大小动态设置超时时间
        if file_size_mb < 100:
            timeout_seconds = 300  # 小文件：5分钟
        elif file_size_mb < 1000:
            timeout_seconds = int(file_size_mb * 3)  # 中等文件：3秒/MB
        else:
            timeout_seconds = int(file_size_mb * 5)  # 大文件：5秒/MB

        timeout_seconds = max(300, min(timeout_seconds, 3600))  # 最少5分钟，最多1小时
        print(f"    文件大小: {file_size_mb:.1f}MB, 超时设置: {timeout_seconds}秒 ({timeout_seconds//60}分钟)")
        print(f"    开始处理时间: {time.strftime('%H:%M:%S')}")

        start_time = time.time()
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout_seconds)
        end_time = time.time()

        print(f"    处理完成时间: {time.strftime('%H:%M:%S')}, 耗时: {end_time-start_time:.1f}秒")

        # 详细的调试信息
        print(f"    返回码: {result.returncode}")
        if result.stdout:
            print(f"    标准输出: {result.stdout}")
        if result.stderr:
            print(f"    错误输出: {result.stderr}")

        if result.returncode == 0:
            # 获取生成的流文件
            flow_files = [f for f in os.listdir(output_dir) if f.endswith('.pcap')]
            print(f"    生成了 {len(flow_files)} 个流文件")
            return [os.path.join(output_dir, f) for f in flow_files]
        else:
            print(f"    SplitCap失败，返回码: {result.returncode}")
            return []

    except subprocess.TimeoutExpired:
        print(f"    SplitCap超时: {os.path.basename(pcap_path)} (文件可能太大)")
        return []
    except Exception as e:
        print(f"    分割流失败: {e}")
        return []

def process_single_flow(flow_path):
    """
    处理单个流文件，提取前5个包的payload - 增强错误处理版本
    """
    try:
        # 检查文件是否存在和可读
        if not os.path.exists(flow_path):
            return None

        # 检查文件大小，跳过异常大的流文件
        try:
            file_size = os.path.getsize(flow_path)
            if file_size > 50 * 1024 * 1024:  # 跳过超过50MB的流文件
                print(f"      跳过大流文件: {os.path.basename(flow_path)} ({file_size/1024/1024:.1f}MB)")
                return None
            if file_size == 0:  # 跳过空文件
                return None
        except:
            return None

        # 首先尝试tshark快速提取
        payload_data = extract_payload_with_tshark(flow_path, max_packets=5)

        # 如果tshark失败，尝试scapy备用方案
        if not payload_data and SCAPY_AVAILABLE:
            payload_data = extract_payload_with_scapy(flow_path, max_packets=5)

        return payload_data

    except Exception as e:
        # 捕获所有异常，避免进程崩溃
        print(f"      流处理异常 {os.path.basename(flow_path)}: {e}")
        return None

def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        memory = psutil.virtual_memory()
        return {
            'used_percent': memory.percent,
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3),
            'total_gb': memory.total / (1024**3)
        }
    except:
        return {'used_percent': 0, 'available_gb': 999, 'used_gb': 0, 'total_gb': 999}

def is_memory_safe(threshold_percent=85):
    """检查内存是否安全"""
    memory_info = get_memory_usage()
    return memory_info['used_percent'] < threshold_percent

def adjust_workers_by_memory(max_workers, flow_count):
    """根据内存情况动态调整工作进程数"""
    memory_info = get_memory_usage()

    if memory_info['used_percent'] > 90:
        # 内存使用超过90%，大幅减少进程数
        adjusted = max(1, max_workers // 4)
        print(f"        内存告警({memory_info['used_percent']:.1f}%)，进程数: {max_workers} → {adjusted}")
    elif memory_info['used_percent'] > 80:
        # 内存使用超过80%，适度减少进程数
        adjusted = max(1, max_workers // 2)
        print(f"        内存警告({memory_info['used_percent']:.1f}%)，进程数: {max_workers} → {adjusted}")
    elif memory_info['available_gb'] < 2:
        # 可用内存少于2GB，保守处理
        adjusted = max(1, min(max_workers, 2))
        print(f"        可用内存不足({memory_info['available_gb']:.1f}GB)，进程数: {max_workers} → {adjusted}")
    else:
        adjusted = max_workers

    return adjusted

def process_flow_batch(flow_files, label_idx):
    """
    处理一批流文件，返回结果列表 - 内存安全版本
    """
    results = []
    # 使用更多CPU核心，但要考虑内存限制
    cpu_count = multiprocessing.cpu_count()
    max_workers = min(max(1, int(cpu_count * 0.9)), len(flow_files))

    # 根据内存情况动态调整进程数
    max_workers = adjust_workers_by_memory(max_workers, len(flow_files))

    memory_info = get_memory_usage()
    print(f"        使用 {max_workers} 个进程处理 {len(flow_files)} 个流 (CPU: {cpu_count}, 内存: {memory_info['used_percent']:.1f}%)")

    try:
        # 使用更保守的进程池设置，避免进程崩溃
        executor_kwargs = {
            'max_workers': max_workers,
            'mp_context': multiprocessing.get_context('spawn')  # 使用spawn方式创建进程，更稳定
        }

        with ProcessPoolExecutor(**executor_kwargs) as executor:
            future_to_flow = {}
            submitted_count = 0

            for flow_file in flow_files:
                # 在提交任务前检查内存
                if not is_memory_safe(threshold_percent=90):
                    print(f"        内存不足，跳过剩余流文件")
                    break

                try:
                    future = executor.submit(process_single_flow, flow_file)
                    future_to_flow[future] = flow_file
                    submitted_count += 1
                except Exception as e:
                    print(f"        提交任务失败 {os.path.basename(flow_file)}: {e}")
                    continue

            completed_count = 0
            failed_count = 0
            print(f"        已提交 {submitted_count} 个流处理任务")

            for future in as_completed(future_to_flow):
                completed_count += 1

                # 每处理50个流检查一次内存和显示进度
                if completed_count % 50 == 0:
                    memory_info = get_memory_usage()
                    print(f"        进度: {completed_count}/{submitted_count}, 成功: {len(results)}, 失败: {failed_count}, 内存: {memory_info['used_percent']:.1f}%")
                    if memory_info['used_percent'] > 95:
                        print(f"        内存危险({memory_info['used_percent']:.1f}%)，强制垃圾回收")
                        gc.collect()

                try:
                    payload_data = future.result(timeout=60)  # 增加到60秒超时
                    if payload_data:
                        # 随机分配到train/dev/test (70%/15%/15%)
                        rand = random.random()
                        if rand < 0.7:
                            dataset_type = 'train'
                        elif rand < 0.85:
                            dataset_type = 'dev'
                        else:
                            dataset_type = 'test'

                        results.append((dataset_type, label_idx, payload_data))

                except concurrent.futures.TimeoutError:
                    flow_file = future_to_flow[future]
                    print(f"        流处理超时 {os.path.basename(flow_file)}: 跳过")
                    failed_count += 1
                except concurrent.futures.process.BrokenProcessPool:
                    flow_file = future_to_flow[future]
                    print(f"        进程池损坏 {os.path.basename(flow_file)}: 跳过")
                    failed_count += 1
                except Exception as e:
                    flow_file = future_to_flow[future]
                    print(f"        批次流处理异常 {os.path.basename(flow_file)}: {e}")
                    failed_count += 1

    except MemoryError:
        print(f"        内存溢出！已处理 {len(results)} 个样本，强制垃圾回收")
        gc.collect()
    except Exception as e:
        print(f"        批次处理异常: {e}")
        gc.collect()

    return results

def process_single_pcap_parallel(args):
    """
    并行处理单个pcap文件的包装函数 - 流级别并行版本
    """
    pcap_path, category, label_idx, temp_base_dir, file_id = args

    try:
        # 检查文件大小并给出内存警告
        file_size_mb = os.path.getsize(pcap_path) / (1024 * 1024)
        print(f"  处理文件: {os.path.basename(pcap_path)} ({file_size_mb:.1f}MB)")

        if file_size_mb > 1000:  # 超过1GB的文件
            print(f"    警告: 大文件可能生成大量流，建议监控内存使用")

        # 为每个pcap文件在系统临时目录创建单独的流输出目录
        pcap_name = os.path.splitext(os.path.basename(pcap_path))[0]
        # 使用系统临时目录，避免污染原始数据集
        system_temp_dir = tempfile.gettempdir()
        file_flow_dir = os.path.join(system_temp_dir, f"vpn_flows_{category}_{file_id}_{pcap_name}_{int(time.time())}")

        # 使用SplitCap分割为流
        flow_files = split_pcap_into_flows(pcap_path, file_flow_dir)

        if not flow_files:
            print(f"    文件 {os.path.basename(pcap_path)}: 分割失败，跳过")
            return {
                'pcap_file': os.path.basename(pcap_path),
                'flows_count': 0,
                'successful_flows': 0,
                'results': []
            }

        results = []
        if flow_files:
            print(f"    开始流级别并行处理 {len(flow_files)} 个流...")

            # 根据流文件数量决定处理策略
            if len(flow_files) > 5000:  # 超过5000个流，分批处理
                print(f"    大量流文件，采用分批处理策略")
                batch_size = 1000  # 每批处理1000个流

                for batch_start in range(0, len(flow_files), batch_size):
                    batch_end = min(batch_start + batch_size, len(flow_files))
                    batch_flows = flow_files[batch_start:batch_end]

                    print(f"      处理批次: {batch_start+1}-{batch_end}/{len(flow_files)}")
                    batch_results = process_flow_batch(batch_flows, label_idx)
                    results.extend(batch_results)

                    # 强制垃圾回收
                    gc.collect()
            else:
                # 正常并行处理 - 使用更多CPU核心，但考虑内存限制
                cpu_count = multiprocessing.cpu_count()
                max_flow_workers = min(max(1, int(cpu_count * 0.9)), len(flow_files))

                # 根据内存情况动态调整
                max_flow_workers = adjust_workers_by_memory(max_flow_workers, len(flow_files))

                memory_info = get_memory_usage()
                print(f"      使用 {max_flow_workers} 个进程处理 {len(flow_files)} 个流 (CPU: {cpu_count}, 内存: {memory_info['used_percent']:.1f}%)")

                with ProcessPoolExecutor(max_workers=max_flow_workers) as flow_executor:
                    # 提交所有流处理任务
                    future_to_flow = {}
                    for flow_file in flow_files:
                        future = flow_executor.submit(process_single_flow, flow_file)
                        future_to_flow[future] = flow_file

                    # 收集结果
                    completed = 0
                    for future in as_completed(future_to_flow):
                        completed += 1
                        if completed % 200 == 0:  # 每200个流显示一次进度
                            print(f"      流处理进度: {completed}/{len(flow_files)}")

                        try:
                            payload_data = future.result()
                            if payload_data:
                                # 随机分配到train/dev/test (70%/15%/15%)
                                rand = random.random()
                                if rand < 0.7:
                                    dataset_type = 'train'
                                elif rand < 0.85:
                                    dataset_type = 'dev'
                                else:
                                    dataset_type = 'test'

                                results.append((dataset_type, label_idx, payload_data))
                        except Exception as e:
                            flow_file = future_to_flow[future]
                            print(f"      流处理异常 {os.path.basename(flow_file)}: {e}")

            print(f"    流级别并行处理完成: {len(results)} 个有效样本")

        # 强制清理临时目录
        cleanup_success = False
        for attempt in range(3):  # 最多尝试3次清理
            try:
                if os.path.exists(file_flow_dir):
                    shutil.rmtree(file_flow_dir)
                    print(f"    临时目录清理成功: {os.path.basename(file_flow_dir)}")
                cleanup_success = True
                break
            except Exception as e:
                print(f"    清理尝试 {attempt+1}/3 失败: {e}")
                time.sleep(1)  # 等待1秒后重试
                gc.collect()  # 强制垃圾回收

        if not cleanup_success:
            print(f"    警告：临时目录清理失败，请手动删除: {file_flow_dir}")

        return {
            'pcap_file': os.path.basename(pcap_path),
            'flows_count': len(flow_files) if flow_files else 0,
            'successful_flows': len(results),
            'results': results
        }

    except Exception as e:
        return {
            'pcap_file': os.path.basename(pcap_path),
            'flows_count': 0,
            'successful_flows': 0,
            'results': [],
            'error': str(e)
        }

def process_batch_to_tsv_parallel(base_path, category, label_idx, pcap_files, tsv_writers, batch_id, max_workers=None):
    """
    并行处理一批pcap文件，按流级别处理，直接写入TSV文件
    """
    # 动态设置工作进程数
    if max_workers is None:
        cpu_count = multiprocessing.cpu_count()
        max_workers = min(max(1, int(cpu_count * 0.6)), len(pcap_files))  # 文件级别用60%CPU

    print(f"  批次 {batch_id}: 并行处理 {len(pcap_files)} 个文件 (工作进程: {max_workers}, 总CPU: {multiprocessing.cpu_count()})...")

    category_path = os.path.join(base_path, category)
    successful_flows = 0
    total_files_processed = 0

    # 使用系统临时目录，避免污染原始数据集
    system_temp_dir = tempfile.gettempdir()
    temp_base_dir = os.path.join(system_temp_dir, f"vpn_batch_flows_{category}_{batch_id}_{int(time.time())}")
    os.makedirs(temp_base_dir, exist_ok=True)
    print(f"    临时目录: {temp_base_dir}")

    try:
        # 准备并行处理的参数
        process_args = []
        for i, pcap_file in enumerate(pcap_files):
            pcap_path = os.path.join(category_path, pcap_file)
            if os.path.exists(pcap_path):
                process_args.append((pcap_path, category, label_idx, temp_base_dir, i))

        if not process_args:
            print(f"    批次 {batch_id}: 没有有效的pcap文件")
            return 0

        # 使用进程池并行处理
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_file = {executor.submit(process_single_pcap_parallel, args): args[0]
                             for args in process_args}

            # 收集结果
            for future in as_completed(future_to_file):
                pcap_path = future_to_file[future]
                try:
                    result = future.result()

                    if 'error' in result:
                        print(f"    文件 {result['pcap_file']} 处理失败: {result['error']}")
                    else:
                        print(f"    文件 {result['pcap_file']}: {result['flows_count']} 个流 → {result['successful_flows']} 个样本")

                        # 写入TSV文件
                        for dataset_type, label, payload_data in result['results']:
                            tsv_writers[dataset_type].writerow([label, payload_data])

                        successful_flows += result['successful_flows']
                        total_files_processed += 1

                except Exception as e:
                    print(f"    文件 {os.path.basename(pcap_path)} 处理异常: {e}")

        print(f"    批次 {batch_id} 完成: {total_files_processed}/{len(pcap_files)} 个文件，生成 {successful_flows} 个流样本")

    finally:
        # 强制清理临时目录
        cleanup_success = False
        for attempt in range(3):  # 最多尝试3次清理
            try:
                if os.path.exists(temp_base_dir):
                    shutil.rmtree(temp_base_dir)
                    print(f"    批次临时目录清理成功: {os.path.basename(temp_base_dir)}")
                cleanup_success = True
                break
            except Exception as e:
                print(f"    批次清理尝试 {attempt+1}/3 失败: {e}")
                time.sleep(1)  # 等待1秒后重试
                gc.collect()  # 强制垃圾回收

        if not cleanup_success:
            print(f"    警告：批次临时目录清理失败，请手动删除: {temp_base_dir}")

        # 强制垃圾回收
        gc.collect()

    return successful_flows

def process_vpn_dataset_batch(base_path, output_path, batch_size=10):
    """
    分批处理VPN数据集，内存友好
    """
    print("=" * 60)
    print("VPN数据集分批处理工具 (内存友好版)")
    print("=" * 60)
    print(f"批次大小: {batch_size} 个文件/批次")
    
    # 创建输出目录
    os.makedirs(output_path, exist_ok=True)
    
    # 创建标签映射
    label_mapping, reverse_mapping = create_label_mapping()
    
    # 保存标签映射
    mapping_file = os.path.join(output_path, "label_mapping.json")
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump({
            "label_to_index": label_mapping,
            "index_to_label": reverse_mapping,
            "total_classes": len(VPN_CATEGORIES)
        }, f, ensure_ascii=False, indent=2)
    
    # 打开TSV文件用于写入
    tsv_files = {}
    tsv_writers = {}
    
    for dataset_name in ['train', 'dev', 'test']:
        tsv_file_path = os.path.join(output_path, f"{dataset_name}_dataset.tsv")
        tsv_files[dataset_name] = open(tsv_file_path, 'w', newline='', encoding='utf-8')
        tsv_writers[dataset_name] = csv.writer(tsv_files[dataset_name], delimiter='\t')
        # 写入头部
        tsv_writers[dataset_name].writerow(["label", "text_a"])
    
    # 文件数量限制 (流级别处理，每个文件会产生多个流样本) - 增加文件数量平衡数据
    max_files_per_class = {
        'clash_doh': 10,       # 增加到10个文件
        'ipsec': 10,           # 增加到10个文件
        'ciscovpn': 10,        # 增加到10个文件
        'openvpn': 10,         # 增加到10个文件
        'wireguard': 10,       # 增加到10个文件
        'SSLVPN': 10,          # 增加到10个文件
        'clash_dot': 10,       # 增加到10个文件
        'l2tp': 10,            # 增加到10个文件
    }
    
    total_samples = 0
    category_stats = {}
    
    try:
        for category in VPN_CATEGORIES:
            print(f"\n处理类别: {category}")
            category_path = os.path.join(base_path, category)
            
            if not os.path.exists(category_path):
                print(f"  跳过: 目录不存在")
                continue
            
            # 获取所有pcap文件
            pcap_files = [f for f in os.listdir(category_path) 
                         if f.endswith('.pcap') and os.path.isfile(os.path.join(category_path, f))]
            
            if not pcap_files:
                print(f"  跳过: 没有pcap文件")
                continue
            
            # 限制文件数量
            max_files = max_files_per_class.get(category, len(pcap_files))
            if len(pcap_files) > max_files:
                pcap_files = random.sample(pcap_files, max_files)
                print(f"  从 {len(os.listdir(category_path))} 个文件中选择 {max_files} 个")
            else:
                print(f"  处理全部 {len(pcap_files)} 个文件")
            
            label_idx = label_mapping[category]
            category_samples = 0
            
            # 分批处理文件 (并行)
            max_workers = min(4, batch_size)  # 限制并行进程数
            for i in range(0, len(pcap_files), batch_size):
                batch_files = pcap_files[i:i+batch_size]
                batch_samples = process_batch_to_tsv_parallel(
                    base_path, category, label_idx, batch_files,
                    tsv_writers, i//batch_size + 1, max_workers
                )
                category_samples += batch_samples
                total_samples += batch_samples
                
                # 每处理几个批次就刷新文件缓冲区
                if (i//batch_size + 1) % 3 == 0:
                    for f in tsv_files.values():
                        f.flush()
            
            category_stats[category] = category_samples
            print(f"  类别 {category} 完成: {category_samples} 个样本")
    
    finally:
        # 关闭所有文件
        for f in tsv_files.values():
            f.close()
    
    # 生成统计信息
    stats_file = os.path.join(output_path, "dataset_stats.txt")
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write("VPN通道数据集统计信息 (分批处理)\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"总样本数: {total_samples}\n")
        f.write(f"处理的类别数: {len(category_stats)}\n\n")
        
        f.write("各类别样本数:\n")
        for category, count in category_stats.items():
            label_idx = label_mapping[category]
            f.write(f"  {label_idx}: {category} - {count} 样本\n")
    
    print("\n" + "=" * 60)
    print("分批处理完成！")
    print("=" * 60)
    print(f"总样本数: {total_samples}")
    print(f"输出目录: {output_path}")
    print("生成的文件:")
    print("- train_dataset.tsv")
    print("- dev_dataset.tsv") 
    print("- test_dataset.tsv")
    print("- dataset_stats.txt")

def main():
    """主函数"""
    base_path = "D:/VPN通道识别/VPN通道数据集/"
    output_path = "D:/VPN通道识别/ET-BERT-main/results/vpn_tunnels_batch/"

    if not os.path.exists(base_path):
        print(f"错误：数据集路径不存在: {base_path}")
        return

    # 设置随机种子
    random.seed(42)

    # 获取CPU核心数
    cpu_count = os.cpu_count()
    print(f"检测到 {cpu_count} 个CPU核心")

    # 分批处理，每批8个文件 (适合并行处理)
    process_vpn_dataset_batch(base_path, output_path, batch_size=8)

if __name__ == "__main__":
    # Windows多进程需要这个保护
    multiprocessing.freeze_support()
    main()
