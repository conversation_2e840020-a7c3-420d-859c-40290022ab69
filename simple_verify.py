#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的配置验证脚本
不依赖torch，仅检查文件和目录结构
"""

import os
import json

def check_files_and_dirs():
    """检查关键文件和目录"""
    print("YaTC VPN微调配置验证")
    print("="*50)
    
    # 检查预训练模型
    model_path = "YaTC-main/output_dir/checkpoint-step150000.pth"
    print(f"\n🔍 检查预训练模型: {model_path}")
    if os.path.exists(model_path):
        size_mb = os.path.getsize(model_path) / (1024*1024)
        print(f"   ✅ 文件存在，大小: {size_mb:.1f} MB")
    else:
        print(f"   ❌ 文件不存在")
    
    # 检查数据集目录
    data_path = "YaTC-main/data/VPN_MFR_balanced"
    print(f"\n🔍 检查数据集: {data_path}")
    if os.path.exists(data_path):
        print(f"   ✅ 数据集目录存在")
        
        # 检查子目录
        for split in ['train', 'val', 'test']:
            split_path = os.path.join(data_path, split)
            if os.path.exists(split_path):
                class_dirs = [d for d in os.listdir(split_path) 
                             if os.path.isdir(os.path.join(split_path, d))]
                print(f"   ✅ {split}: {len(class_dirs)} 个类别")
            else:
                print(f"   ❌ 缺少 {split} 目录")
        
        # 检查类别权重文件
        weights_file = os.path.join(data_path, 'class_weights.json')
        if os.path.exists(weights_file):
            print(f"   ✅ 类别权重文件存在")
        else:
            print(f"   ⚠️  类别权重文件不存在")
    else:
        print(f"   ❌ 数据集目录不存在")
    
    # 检查fine-tune.py
    fine_tune_path = "YaTC-main/fine-tune.py"
    print(f"\n🔍 检查配置文件: {fine_tune_path}")
    if os.path.exists(fine_tune_path):
        print(f"   ✅ fine-tune.py存在")
        
        # 检查关键配置
        with open(fine_tune_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "checkpoint-step150000.pth" in content:
            print(f"   ✅ 预训练模型路径已配置")
        else:
            print(f"   ⚠️  预训练模型路径可能需要检查")
        
        if "default=14" in content:
            print(f"   ✅ 类别数量设置为14")
        else:
            print(f"   ⚠️  类别数量可能需要检查")
        
        if "VPN_MFR_balanced" in content:
            print(f"   ✅ 数据集路径已配置")
        else:
            print(f"   ⚠️  数据集路径可能需要检查")
    else:
        print(f"   ❌ fine-tune.py不存在")
    
    print(f"\n🚀 训练命令:")
    print("cd YaTC-main")
    print("python fine-tune.py")
    
    print(f"\n📝 配置摘要:")
    print(f"- 预训练模型: checkpoint-step150000.pth")
    print(f"- 数据集: VPN_MFR_balanced (14类)")
    print(f"- 训练轮数: 50 epochs")
    print(f"- 支持类别权重和早停")

if __name__ == "__main__":
    check_files_and_dirs()
