
name: my_ytc_env  # 您想要给环境起的名字

channels:
  - pytorch      # 优先从PyTorch官方源下载
  - conda-forge  # 其次是conda-forge，它包罗万象
  - defaults     # 最后是默认源

dependencies:
  # --- 核心依赖 (Conda包) ---
  - python=3.8
  - pytorch=1.8.0
  - torchvision=0.9.0
  - torchaudio=0.8.0
  - cudatoolkit=11.1   # 指定CUDA主版本即可，Conda会自动匹配
  - numpy=1.21
  - pandas=1.4
  - scikit-learn=1.1.1
  - scipy=1.8.1
  - matplotlib=3.5
  - seaborn=0.11
  - cython
  - flask
  - pillow=10.0      # Pillow 10.0.0 有个重要改动，如果项目老，可能需要降级到9.x
  - pyyaml
  - tqdm
  - requests
  - h5py           # DGL 可能需要
  - dgl            # 我们需要DGL，直接在这里声明
  - scapy          # 我们需要scapy，直接在这里声明

  # --- 其他依赖 (通过 pip 安装) ---
  # 将原始文件中标为 pypi 的包放在这里
  # pip 会在所有 conda 包安装完成后自动执行
  - pip:
    - opencv-python==********
    - torchsummary==1.5.1
    - lightgbm==3.3.3
    - timm==0.3.2
    - flowcontainer==6.2
    - libmr==0.1.9
    # 如果还有其他 pip 包，可以在下面继续添加