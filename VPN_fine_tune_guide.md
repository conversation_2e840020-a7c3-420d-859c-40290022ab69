# YaTC VPN通道数据集微调指南

## 📋 修改总览

我已经成功修改了YaTC的`fine-tune.py`脚本，使其完全适配VPN通道数据集。以下是主要的修改内容：

### 🔧 关键修改

1. **类别数量**: 从7类改为14类VPN通道
2. **数据路径**: 更新为VPN_MFR_balanced目录
3. **验证集支持**: 添加独立的验证集处理
4. **类别权重**: 集成类别权重处理数据不平衡
5. **早停机制**: 防止过拟合
6. **增强评估**: 更详细的评估指标和日志

### 📊 新增功能

- ✅ 支持train/val/test三分割数据集
- ✅ 自动加载类别权重配置
- ✅ 早停机制 (默认15个epoch patience)
- ✅ 训练时数据增强 (旋转、翻转)
- ✅ 详细的训练日志和TensorBoard记录
- ✅ 最终测试集评估和结果保存

## 🚀 使用方法

### 1. 基础训练命令

```bash
cd YaTC-main

# 使用默认参数训练
python fine-tune.py

# 自定义参数训练
python fine-tune.py \
    --data_path ./data/VPN_MFR_balanced \
    --nb_classes 14 \
    --batch_size 32 \
    --epochs 100 \
    --lr 1e-4 \
    --output_dir ./output_vpn \
    --log_dir ./logs_vpn
```

### 2. 高级配置

```bash
# 使用类别权重 + 早停
python fine-tune.py \
    --data_path ./data/VPN_MFR_balanced \
    --use_class_weights \
    --class_weights_file class_weights.json \
    --early_stopping_patience 20 \
    --output_dir ./output_vpn_weighted

# 仅评估模式
python fine-tune.py \
    --eval \
    --resume ./output_vpn/checkpoint-best.pth \
    --data_path ./data/VPN_MFR_balanced
```

### 3. 分布式训练

```bash
# 多GPU训练
python -m torch.distributed.launch \
    --nproc_per_node=2 \
    fine-tune.py \
    --data_path ./data/VPN_MFR_balanced \
    --batch_size 32 \
    --output_dir ./output_vpn_distributed
```

## 📁 目录结构要求

确保您的数据集目录结构如下：

```
data/VPN_MFR_balanced/
├── train/
│   ├── CiscoVPN/
│   ├── Clash_DoH/
│   ├── IPSec/
│   ├── L2TP/
│   ├── OpenVPN/
│   ├── OpenVPN_Vless/
│   ├── OpenVPN_Vmess/
│   ├── SSLVPN/
│   ├── SSR_Vmess/
│   ├── SS_Vmess/
│   ├── Vless/
│   ├── Vmess/
│   ├── Wireguard/
│   └── Wireguard_SSH/
├── val/
│   └── [相同的14个类别目录]
├── test/
│   └── [相同的14个类别目录]
└── class_weights.json
```

## ⚙️ 重要参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--data_path` | `./data/VPN_MFR_balanced` | 数据集根目录 |
| `--nb_classes` | `14` | VPN类别数量 |
| `--batch_size` | `64` | 批次大小 |
| `--epochs` | `200` | 训练轮数 |
| `--lr` | `自动计算` | 学习率 |
| `--use_class_weights` | `True` | 是否使用类别权重 |
| `--early_stopping_patience` | `15` | 早停耐心值 |
| `--class_weights_file` | `class_weights.json` | 类别权重文件 |

## 📊 输出文件说明

训练完成后，会在输出目录生成以下文件：

```
output_dir/
├── checkpoint-best.pth          # 最佳模型权重
├── checkpoint-last.pth          # 最后一轮模型权重
├── final_results.json           # 最终评估结果
└── log.txt                      # 训练日志
```

### final_results.json 内容示例

```json
{
  "best_epoch": 45,
  "best_val_f1": 0.8234,
  "best_val_accuracy": 0.8456,
  "final_test_accuracy": 0.8123,
  "final_test_f1": 0.8067,
  "final_test_precision": 0.8145,
  "final_test_recall": 0.8089,
  "training_time": "2:34:56",
  "class_names": ["CiscoVPN", "Clash_DoH", ...]
}
```

## 🔍 监控训练过程

### 1. TensorBoard监控

```bash
# 启动TensorBoard
tensorboard --logdir ./logs_vpn

# 在浏览器中访问
http://localhost:6006
```

### 2. 实时日志

训练过程中会显示详细的进度信息：

```
Epoch 10:
  Validation - Accuracy: 0.7234, F1: 0.7123
  New best model saved! F1: 0.7123, Accuracy: 0.7234
  Best so far - Epoch: 10, F1: 0.7123, Accuracy: 0.7234
```

## ⚠️ 注意事项

### 1. 内存要求
- 推荐至少8GB GPU内存
- 如果内存不足，可以减少batch_size

### 2. 类别权重
- 确保`class_weights.json`文件存在于数据集目录中
- 如果不使用类别权重，添加`--no_use_class_weights`参数

### 3. 预训练模型
- 默认使用`./output_dir/pretrained-model.pth`
- 可以通过`--finetune`参数指定其他预训练模型

### 4. 早停机制
- 基于验证集F1分数进行早停
- 可以通过`--early_stopping_patience`调整耐心值

## 🐛 常见问题

### Q1: 找不到类别权重文件
```
Warning: Could not load class weights from class_weights.json
```
**解决方案**: 确保在数据集目录中有`class_weights.json`文件，或使用`--no_use_class_weights`

### Q2: CUDA内存不足
```
RuntimeError: CUDA out of memory
```
**解决方案**: 减少batch_size，例如`--batch_size 16`

### Q3: 数据集目录结构错误
```
FileNotFoundError: [Errno 2] No such file or directory
```
**解决方案**: 检查数据集目录结构，确保包含train/val/test三个子目录

## 🎯 性能优化建议

### 1. 超参数调优
- 学习率: 尝试1e-4到1e-3之间的值
- 批次大小: 根据GPU内存调整，32-64通常效果较好
- 早停耐心值: 对于小数据集可以设置为10-15

### 2. 数据增强
- 当前已包含旋转和翻转
- 可以在`build_dataset`函数中添加更多增强方法

### 3. 模型集成
- 训练多个模型并进行集成
- 使用不同的随机种子和超参数

---

**祝您训练顺利！如有问题，请检查日志文件或联系技术支持。**
