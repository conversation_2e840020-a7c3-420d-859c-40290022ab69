import argparse
import torch
import numpy as np
from pathlib import Path
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
import models_YaTC
import util.misc as misc
from engine import evaluate
import torch.backends.cudnn as cudnn
from torchvision import datasets, transforms
import os

def build_dataset(is_train, args):
    mean = [0.5]
    std = [0.5]

    transform = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),
        transforms.ToTensor(),
        transforms.Normalize(mean, std),
    ])
    root = os.path.join(args.data_path, 'train' if is_train else 'test')
    dataset = datasets.ImageFolder(root, transform=transform)

    print(dataset)

    return dataset

def get_args_parser():
    parser = argparse.ArgumentParser('YaTC evaluation', add_help=False)
    parser.add_argument('--batch_size', default=16, type=int)
    parser.add_argument('--model', default='TraFormer_YaTC', type=str)
    parser.add_argument('--input_size', default=40, type=int)
    parser.add_argument('--device', default='cuda')
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--num_workers', default=0, type=int)
    parser.add_argument('--pin_mem', default=True, action='store_true')
    parser.add_argument('--nb_classes', default=8, type=int)
    parser.add_argument('--data_path', default='./data', type=str)
    parser.add_argument('--checkpoint', default='./output_dir/my_finetuned_model/checkpoint-best.pth',
                        help='checkpoint path')
    parser.add_argument('--output_dir', default='./eval_results',
                        help='path where to save evaluation results')
    return parser

def plot_confusion_matrix(cm, classes, save_path):
    """绘制混淆矩阵"""
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=classes,
                yticklabels=classes)
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    # 重置字体设置，避免影响其他图
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = True

def main(args):
    print('Evaluating with args:', args)

    # 创建输出目录
    Path(args.output_dir).mkdir(parents=True, exist_ok=True)

    # 设置设备
    device = torch.device(args.device)

    # 固定随机种子
    seed = args.seed
    torch.manual_seed(seed)
    np.random.seed(seed)
    cudnn.benchmark = True

    # 构建数据集和数据加载器
    dataset_val = build_dataset(is_train=False, args=args)
    sampler_val = torch.utils.data.SequentialSampler(dataset_val)
    data_loader_val = torch.utils.data.DataLoader(
        dataset_val, sampler=sampler_val,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        pin_memory=args.pin_mem,
        drop_last=False
    )

    # 构建模型
    model = models_YaTC.__dict__[args.model](
        num_classes=args.nb_classes,
        drop_path_rate=0.1,  # 使用默认值
    )

    # 加载检查点
    checkpoint = torch.load(args.checkpoint, map_location='cpu')
    msg = model.load_state_dict(checkpoint['model'])
    print('Loaded checkpoint from:', args.checkpoint)
    print('Load state dict message:', msg)

    model.to(device)
    model.eval()

    # 进行评估
    test_stats = evaluate(data_loader_val, model, device)
    
    # 保存评估结果
    results_file = Path(args.output_dir) / 'evaluation_results.txt'
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"Top-1 Accuracy: {test_stats['acc1']:.2f}%\n")
        f.write(f"Top-5 Accuracy: {test_stats['acc5']:.2f}%\n")
        f.write(f"Macro Average Precision: {test_stats['macro_pre']:.4f}\n")
        f.write(f"Macro Average Recall: {test_stats['macro_rec']:.4f}\n")
        f.write(f"Macro Average F1 Score: {test_stats['macro_f1']:.4f}\n\n")

        # --- 新增：打印每个类别的详细报告 ---
        report = classification_report(
            test_stats['all_labels'],
            test_stats['all_preds'],
            target_names=dataset_val.classes,
            digits=4
        )
        f.write("Classification Report:\n")
        f.write(report)
        # --- 结束新增 ---

        f.write("\n\nConfusion Matrix:\n")
        # 格式化打印混淆矩阵以便对齐
        cm_str = np.array2string(test_stats['cm'], separator=', ', formatter={'int': lambda x: f"{x:5d}"})
        f.write(cm_str)

    # 绘制混淆矩阵
    plot_confusion_matrix(
        test_stats['cm'],
        classes=dataset_val.classes,
        save_path=str(Path(args.output_dir) / 'confusion_matrix.png')
    )

    print('Evaluation completed. Results saved in:', args.output_dir)
    # --- 修改：打印更详细的报告到控制台 ---
    print("\n" + "="*50)
    print(f"Overall Top-1 Accuracy: {test_stats['acc1']:.2f}%")
    print(f"Overall Macro F1 Score: {test_stats['macro_f1']:.4f}")
    print("="*50 + "\n")
    
    report = classification_report(
        test_stats['all_labels'],
        test_stats['all_preds'],
        target_names=dataset_val.classes,
        digits=4
    )
    print("--- Classification Report ---")
    print(report)
    print("-----------------------------\n")
    # --- 结束修改 ---


if __name__ == '__main__':
    args = get_args_parser().parse_args()
    main(args) 