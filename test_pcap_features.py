#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接从pcap文件提取特征
"""

import os
import sys
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# 添加YaTC-main目录到Python路径
sys.path.insert(0, 'YaTC-main')

try:
    import scapy.all as scapy
    import binascii
    print("✓ 成功导入scapy")
except ImportError as e:
    print(f"✗ 导入scapy失败: {e}")
    sys.exit(1)

def extract_flow_features(pcap_file):
    """
    从pcap文件提取前5个包的特征
    """
    try:
        print(f"处理文件: {pcap_file}")
        
        # 读取pcap文件，只读取前10个包
        packets = scapy.rdpcap(str(pcap_file), count=10)
        print(f"读取到 {len(packets)} 个数据包")
        
        if not packets:
            print("没有数据包，返回全零矩阵")
            return np.zeros((40, 40), dtype=np.uint8)
        
        # 提取前5个包的特征
        features = []
        packet_count = 0
        
        for i, packet in enumerate(packets):
            if packet_count >= 5:
                break
                
            try:
                # 获取包的原始字节
                packet_bytes = bytes(packet)
                print(f"  包 {i+1}: {len(packet_bytes)} 字节")
                
                # 取前320字节（如果不足则用0填充）
                if len(packet_bytes) >= 320:
                    packet_data = packet_bytes[:320]
                else:
                    packet_data = packet_bytes + b'\x00' * (320 - len(packet_bytes))
                
                # 转换为数值列表
                packet_values = list(packet_data)
                features.extend(packet_values)
                packet_count += 1
                
                # 显示前几个字节
                print(f"    前10字节: {packet_values[:10]}")
                
            except Exception as e:
                print(f"    处理包 {i+1} 失败: {e}")
                # 如果处理包失败，用零填充
                features.extend([0] * 320)
                packet_count += 1
        
        # 如果包数不足5个，用零填充
        while packet_count < 5:
            print(f"  填充包 {packet_count + 1}")
            features.extend([0] * 320)
            packet_count += 1
        
        print(f"总特征数: {len(features)}")
        
        # 现在features应该有5*320=1600个值
        # 重塑为40x40矩阵
        feature_array = np.array(features[:1600], dtype=np.uint8)
        feature_matrix = feature_array.reshape(40, 40)
        
        print(f"特征矩阵形状: {feature_matrix.shape}")
        print(f"像素值范围: {feature_matrix.min()} - {feature_matrix.max()}")
        print(f"平均值: {feature_matrix.mean():.2f}")
        print(f"非零值比例: {(feature_matrix != 0).sum() / feature_matrix.size:.2%}")
        
        return feature_matrix
        
    except Exception as e:
        print(f"处理文件失败 {pcap_file}: {e}")
        return np.zeros((40, 40), dtype=np.uint8)

def test_pcap_extraction():
    """测试pcap特征提取"""
    print("测试pcap特征提取")
    print("="*50)
    
    # 查找测试用的pcap文件
    vpn_dataset_path = "VPN通道数据集"
    
    if not os.path.exists(vpn_dataset_path):
        print(f"VPN数据集路径不存在: {vpn_dataset_path}")
        return
    
    test_files = []
    
    # 从每个类别选择一个文件
    for class_dir in os.listdir(vpn_dataset_path):
        class_path = os.path.join(vpn_dataset_path, class_dir)
        if os.path.isdir(class_path):
            pcap_files = [f for f in os.listdir(class_path) if f.endswith('.pcap')]
            if pcap_files:
                test_file = os.path.join(class_path, pcap_files[0])
                test_files.append((class_dir, test_file))
                if len(test_files) >= 5:  # 只测试5个类别
                    break
    
    if not test_files:
        print("未找到测试文件")
        return
    
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, (class_name, pcap_file) in enumerate(test_files):
        print(f"\n--- 测试类别: {class_name} ---")
        
        # 提取特征
        feature_matrix = extract_flow_features(pcap_file)
        
        # 可视化
        if i < len(axes):
            axes[i].imshow(feature_matrix, cmap='gray')
            axes[i].set_title(f'{class_name}\n平均值: {feature_matrix.mean():.1f}')
            axes[i].axis('off')
    
    # 隐藏多余的子图
    for i in range(len(test_files), len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('pcap_features_test.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"\n✅ 测试完成!")
    print(f"可视化结果保存到: pcap_features_test.png")

def compare_with_mfr():
    """与现有MFR图像对比"""
    print("\n" + "="*50)
    print("与现有MFR图像对比")
    print("="*50)
    
    # 找一个类别进行对比
    mfr_path = "YaTC-main/data/VPN_MFR/openvpn"
    pcap_path = "VPN通道数据集/openvpn"
    
    if not os.path.exists(mfr_path) or not os.path.exists(pcap_path):
        print("对比路径不存在")
        return
    
    # 找到对应的文件
    mfr_files = [f for f in os.listdir(mfr_path) if f.endswith('.png')]
    pcap_files = [f for f in os.listdir(pcap_path) if f.endswith('.pcap')]
    
    if not mfr_files or not pcap_files:
        print("未找到对比文件")
        return
    
    # 选择第一个文件进行对比
    mfr_file = os.path.join(mfr_path, mfr_files[0])
    pcap_file = os.path.join(pcap_path, pcap_files[0])
    
    print(f"MFR文件: {mfr_file}")
    print(f"PCAP文件: {pcap_file}")
    
    # 加载MFR图像
    mfr_img = Image.open(mfr_file)
    mfr_array = np.array(mfr_img)
    
    # 提取PCAP特征
    pcap_array = extract_flow_features(pcap_file)
    
    # 对比
    print(f"\nMFR图像:")
    print(f"  形状: {mfr_array.shape}")
    print(f"  像素值范围: {mfr_array.min()} - {mfr_array.max()}")
    print(f"  平均值: {mfr_array.mean():.2f}")
    print(f"  非零值比例: {(mfr_array != 0).sum() / mfr_array.size:.2%}")
    
    print(f"\nPCAP特征:")
    print(f"  形状: {pcap_array.shape}")
    print(f"  像素值范围: {pcap_array.min()} - {pcap_array.max()}")
    print(f"  平均值: {pcap_array.mean():.2f}")
    print(f"  非零值比例: {(pcap_array != 0).sum() / pcap_array.size:.2%}")
    
    # 可视化对比
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    ax1.imshow(mfr_array, cmap='gray')
    ax1.set_title(f'原始MFR\n平均值: {mfr_array.mean():.1f}')
    ax1.axis('off')
    
    ax2.imshow(pcap_array, cmap='gray')
    ax2.set_title(f'直接PCAP提取\n平均值: {pcap_array.mean():.1f}')
    ax2.axis('off')
    
    plt.tight_layout()
    plt.savefig('mfr_vs_pcap_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"对比图保存到: mfr_vs_pcap_comparison.png")

def main():
    """主函数"""
    print("PCAP特征提取测试工具")
    print("="*60)
    
    # 测试pcap特征提取
    test_pcap_extraction()
    
    # 与MFR对比
    compare_with_mfr()
    
    print("\n" + "="*60)
    print("测试完成!")
    print("如果PCAP特征提取效果更好，可以使用以下命令训练:")
    print("python fine-tune.py --use_pcap_direct --pcap_data_path ../VPN通道数据集")

if __name__ == "__main__":
    main()
