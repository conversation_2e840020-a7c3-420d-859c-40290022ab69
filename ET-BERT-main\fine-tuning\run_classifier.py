"""
This script provides an example to wrap UER-py for classification.
MODIFIED VERSION to fix model training issues.
Key changes:
1.  Force usage of command-line `--labels_num`.
2.  Corrected confusion matrix calculation to `[true, pred]`.
3.  Switched to the more stable `nn.CrossEntropyLoss`.
4.  Refactored class weight calculation to be more robust.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import argparse
import torch
import torch.nn as nn
from uer.layers import *
from uer.encoders import *
from uer.utils.vocab import Vocab
from uer.utils.constants import *
from uer.utils import *
from uer.utils.optimizers import *
from uer.utils.config import load_hyperparam
from uer.utils.seed import set_seed
from uer.model_saver import save_model
from uer.opts import finetune_opts
import tqdm
from torch.utils.data import Dataset, DataLoader, IterableDataset
from collections import Counter

class Classifier(nn.Module):
    def __init__(self, args):
        super(Classifier, self).__init__()
        self.embedding = str2embedding[args.embedding](args, len(args.tokenizer.vocab))
        self.encoder = str2encoder[args.encoder](args)
        self.labels_num = args.labels_num
        self.pooling = args.pooling
        self.output_layer_1 = nn.Linear(args.hidden_size, args.hidden_size)
        self.output_layer_2 = nn.Linear(args.hidden_size, self.labels_num)
        
        # Instantiate loss function here for stability and clarity
        self.loss_fn = nn.CrossEntropyLoss(weight=args.class_weights)

    def forward(self, src, tgt, seg):
        """
        Args:
            src: [batch_size x seq_length]
            tgt: [batch_size] or None for inference
            seg: [batch_size x seq_length]
        """
        # Embedding
        emb = self.embedding(src, seg)
        # Encoder
        output = self.encoder(emb, seg)
        # Target
        if self.pooling == "mean":
            output = torch.mean(output, dim=1)
        elif self.pooling == "max":
            output = torch.max(output, dim=1)[0]
        elif self.pooling == "last":
            output = output[:, -1, :]
        else:
            output = output[:, 0, :]
        
        output = torch.tanh(self.output_layer_1(output))
        logits = self.output_layer_2(output)
        
        if tgt is not None:
            loss = self.loss_fn(logits, tgt.view(-1))
            return loss, logits
        else:
            return None, logits


def load_or_initialize_parameters(args, model):
    if args.pretrained_model_path is not None:
        pretrained_state_dict = torch.load(args.pretrained_model_path, map_location='cpu')
        model_state_dict = model.state_dict()
        
        pretrained_state_dict_filtered = {}
        for k, v in pretrained_state_dict.items():
            if k in model_state_dict and model_state_dict[k].shape == v.shape:
                pretrained_state_dict_filtered[k] = v
            else:
                print(f"Skipping loading parameter '{k}' with shape {v.shape} due to shape mismatch with current model shape {model_state_dict.get(k, 'N/A')}.")
        
        model.load_state_dict(pretrained_state_dict_filtered, strict=False)
    else:
        for n, p in list(model.named_parameters()):
            if "gamma" not in n and "beta" not in n:
                p.data.normal_(0, 0.02)


def build_optimizer(args, model):
    param_optimizer = list(model.named_parameters())
    no_decay = ['bias', 'gamma', 'beta']
    optimizer_grouped_parameters = [
        {'params': [p for n, p in param_optimizer if not any(nd in n for nd in no_decay)], 'weight_decay_rate': 0.01},
        {'params': [p for n, p in param_optimizer if any(nd in n for nd in no_decay)], 'weight_decay_rate': 0.0}
    ]
    if args.optimizer in ["adamw"]:
        optimizer = str2optimizer[args.optimizer](optimizer_grouped_parameters, lr=args.learning_rate, correct_bias=False)
    else:
        optimizer = str2optimizer[args.optimizer](optimizer_grouped_parameters, lr=args.learning_rate, scale_parameter=False, relative_step=False)
    
    if args.scheduler in ["constant"]:
        scheduler = str2scheduler[args.scheduler](optimizer)
    elif args.scheduler in ["constant_with_warmup"]:
        scheduler = str2scheduler[args.scheduler](optimizer, int(args.train_steps * args.warmup))
    else:
        scheduler = str2scheduler[args.scheduler](optimizer, int(args.train_steps * args.warmup), args.train_steps)
    return optimizer, scheduler


def process_line(line, columns, tokenizer, seq_length, label_to_index=None):
    line = line.strip().split("\t")
    original_label = int(line[columns["label"]])

    # 如果有标签映射，使用映射后的索引；否则使用原始标签
    if label_to_index is not None:
        if original_label in label_to_index:
            label = label_to_index[original_label]
        else:
            raise ValueError(f"Label {original_label} not found in label mapping")
    else:
        label = original_label

    text_a = line[columns["text_a"]]
    src = tokenizer.convert_tokens_to_ids([CLS_TOKEN] + tokenizer.tokenize(text_a))
    seg = [1] * len(src)

    if len(src) > seq_length:
        src = src[:seq_length]
        seg = seg[:seq_length]
    while len(src) < seq_length:
        src.append(0)
        seg.append(0)

    return (src, label, seg)


class LineByLineTextDataset(IterableDataset):
    def __init__(self, path, tokenizer, seq_length, label_to_index=None):
        self.path = path
        self.tokenizer = tokenizer
        self.seq_length = seq_length
        self.label_to_index = label_to_index
        with open(path, "r", encoding="utf-8") as f:
            self.total_lines = sum(1 for _ in f) - 1

    def __iter__(self):
        with open(self.path, "r", encoding="utf-8") as f:
            header_line = f.readline()
            columns = {name: i for i, name in enumerate(header_line.strip().split("\t"))}
            for line in f:
                yield process_line(line, columns, self.tokenizer, self.seq_length, self.label_to_index)


def collate_fn(batch):
    src_list, tgt_list, seg_list = [], [], []
    for sample in batch:
        src_list.append(sample[0])
        tgt_list.append(sample[1])
        seg_list.append(sample[2])
    return torch.LongTensor(src_list), torch.LongTensor(tgt_list), torch.LongTensor(seg_list)


def train_model(args, model, optimizer, scheduler, src_batch, tgt_batch, seg_batch):
    model.train()
    model.zero_grad()

    src_batch = src_batch.to(args.device)
    tgt_batch = tgt_batch.to(args.device)
    seg_batch = seg_batch.to(args.device)

    loss, _ = model(src_batch, tgt_batch, seg_batch)
    if torch.cuda.device_count() > 1:
        loss = torch.mean(loss)

    if args.fp16:
        with args.amp.scale_loss(loss, optimizer) as scaled_loss:
            scaled_loss.backward()
    else:
        loss.backward()

    # 梯度裁剪防止梯度爆炸
    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

    optimizer.step()
    scheduler.step()
    return loss


def evaluate(args, model, dataset, print_confusion_matrix=False):
    # 评估时也禁用多进程以保持一致性
    eval_dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        collate_fn=collate_fn,
        num_workers=0,
        pin_memory=True if torch.cuda.is_available() else False
    )
    correct, total = 0, 0
    confusion = torch.zeros(args.labels_num, args.labels_num, dtype=torch.long)

    model.eval()
    with torch.no_grad():
        for src_batch, tgt_batch, seg_batch in eval_dataloader:
            src_batch = src_batch.to(args.device)
            tgt_batch = tgt_batch.to(args.device)
            seg_batch = seg_batch.to(args.device)
            
            _, logits = model(src_batch, None, seg_batch)
            pred = torch.argmax(logits, dim=1)
            gold = tgt_batch
            
            correct += pred.eq(gold).sum().item()
            total += src_batch.size(0)

            # Update confusion matrix: rows are true labels, columns are predicted labels
            for j in range(pred.size(0)):
                confusion[gold[j], pred[j]] += 1
    
    if total == 0:
        print("Warning: No samples found in the evaluation set.")
        return 0.0, confusion
    
    acc = correct / total
    print(f"\nAcc. (Correct/Total): {acc:.4f} ({correct}/{total})")
    
    if print_confusion_matrix:
        print("Confusion matrix (Rows: True, Columns: Predicted):")
        print(confusion)
        tp = confusion.diag()
        for i in range(args.labels_num):
            precision = tp[i] / confusion[:, i].sum().item() if confusion[:, i].sum().item() > 0 else 0
            recall = tp[i] / confusion[i, :].sum().item() if confusion[i, :].sum().item() > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            print(f"Class {i:2d}: P={precision:.4f}, R={recall:.4f}, F1={f1:.4f}")

    return acc, confusion


def calculate_class_weights(args):
    print("\nCalculating class weights from training data...")
    label_counts = Counter()
    with open(args.train_path, "r", encoding="utf-8") as f:
        header_line = f.readline()
        columns = {name: i for i, name in enumerate(header_line.strip().split("\t"))}
        for line in f:
            label = int(line.strip().split("\t")[columns["label"]])
            label_counts[label] += 1

    total_samples = sum(label_counts.values())
    print(f"Total training samples: {total_samples}")

    # 获取实际存在的标签
    actual_labels = sorted(label_counts.keys())
    print(f"Actual labels found: {actual_labels}")

    # 显示每个实际标签的统计
    for label in actual_labels:
        count = label_counts[label]
        percentage = (count / total_samples) * 100 if total_samples > 0 else 0
        print(f"  Label {label}: {count} samples ({percentage:.2f}%)")

    # 创建标签映射：将实际标签映射到连续的索引
    label_to_index = {label: idx for idx, label in enumerate(actual_labels)}
    print(f"Label mapping: {label_to_index}")

    # 计算权重（只为实际存在的标签）
    weights = []
    actual_labels_num = len(actual_labels)
    for label in actual_labels:
        count = label_counts[label]
        weight = total_samples / (actual_labels_num * count)
        weights.append(weight)

    print("\nApplied class weights to loss function:")
    for idx, (label, weight) in enumerate(zip(actual_labels, weights)):
        print(f"  Label {label} (index {idx}): weight={weight:.2f}")

    return torch.FloatTensor(weights).to(args.device), label_to_index


def main():
    parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    finetune_opts(parser)

    parser.add_argument("--pooling", choices=["mean", "max", "first", "last"], default="first")
    parser.add_argument("--tokenizer", choices=["bert", "char", "space"], default="bert")
    # This argument is now CRITICAL for correct model setup.
    parser.add_argument("--labels_num", type=int, required=True, help="Number of classification labels.")
    parser.add_argument("--use_class_weights", action="store_true", help="Enable class weighting for the loss function.")

    args = parser.parse_args()
    args = load_hyperparam(args)

    set_seed(args.seed)
    
    args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    args.tokenizer = str2tokenizer[args.tokenizer](args)
    
    # Calculate class weights if requested
    if args.use_class_weights:
        args.class_weights, args.label_to_index = calculate_class_weights(args)
        # 更新实际的标签数量
        args.labels_num = len(args.label_to_index)
        print(f"Updated labels_num to {args.labels_num} based on actual data")
    else:
        args.class_weights = None
        args.label_to_index = None
        
    model = Classifier(args)
    
    load_or_initialize_parameters(args, model)
    model.to(args.device)

    train_dataset = LineByLineTextDataset(args.train_path, args.tokenizer, args.seq_length, args.label_to_index)
    dev_dataset = LineByLineTextDataset(args.dev_path, args.tokenizer, args.seq_length, args.label_to_index)
    test_dataset = LineByLineTextDataset(args.test_path, args.tokenizer, args.seq_length, args.label_to_index)
    
    args.train_steps = (train_dataset.total_lines // args.batch_size) * args.epochs_num
    # 对于IterableDataset，不能使用shuffle=True
    # 添加多进程数据加载和内存固定以提升GPU训练速度
    # 暂时禁用多进程以避免进度条显示问题
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        collate_fn=collate_fn,
        num_workers=0,  # 禁用多进程
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    optimizer, scheduler = build_optimizer(args, model)
    
    if args.fp16:
        try:
            from apex import amp
            model, optimizer = amp.initialize(model, optimizer, opt_level=args.fp16_opt_level)
            args.amp = amp
        except ImportError:
            raise ImportError("Please install apex from https://www.github.com/nvidia/apex to use fp16 training.")

    if torch.cuda.device_count() > 1:
        print(f"{torch.cuda.device_count()} GPUs are available. Using DataParallel.")
        model = nn.DataParallel(model)

    print("\n***** Running training *****")
    print(f"  Num examples = {train_dataset.total_lines}")
    print(f"  Num Epochs = {args.epochs_num}")
    print(f"  Batch size = {args.batch_size}")
    print(f"  Num labels = {args.labels_num}")
    
    best_accuracy = 0.0
    best_epoch = 0

    for epoch in range(1, args.epochs_num + 1):
        model.train()
        steps_per_epoch = (train_dataset.total_lines + args.batch_size - 1) // args.batch_size

        # 修复进度条显示问题：禁用多进程时的进度条或使用简单的计数器
        use_progress_bar = True
        if use_progress_bar:
            progress_bar = tqdm.tqdm(
                total=steps_per_epoch,
                desc=f"Epoch {epoch}",
                unit="batch",
                ncols=100,  # 固定宽度避免显示问题
                leave=True   # 保留进度条
            )

        total_loss, total_instances = 0.0, 0
        step_count = 0

        for src_batch, tgt_batch, seg_batch in train_dataloader:
            loss = train_model(args, model, optimizer, scheduler, src_batch, tgt_batch, seg_batch)
            total_loss += loss.item() * src_batch.size(0)
            total_instances += src_batch.size(0)
            step_count += 1

            if total_instances > 0:
                avg_loss = total_loss / total_instances
                if use_progress_bar:
                    progress_bar.set_postfix(loss=f"{avg_loss:.4f}")
                    progress_bar.update(1)
                else:
                    # 简单的文本进度显示
                    if step_count % 100 == 0 or step_count == steps_per_epoch:
                        print(f"Epoch {epoch}: {step_count}/{steps_per_epoch} batches, loss={avg_loss:.4f}")

        if use_progress_bar:
            progress_bar.close()
            
        print(f"\n--- Finished Epoch {epoch} ---")
        current_accuracy, _ = evaluate(args, model, dev_dataset, print_confusion_matrix=True)

        if current_accuracy > best_accuracy:
            best_accuracy = current_accuracy
            best_epoch = epoch
            print(f"🎉 New best accuracy on dev set: {best_accuracy:.4f} (Epoch {epoch})")
            save_model(model, args.output_model_path)
            print(f"Best model saved to: {args.output_model_path}")
        else:
            print(f"Dev accuracy: {current_accuracy:.4f}, Best accuracy remains: {best_accuracy:.4f} (Epoch {best_epoch})")

    print("\n***** Training finished *****")
    print(f"Loading best model from epoch {best_epoch} for final evaluation...")
    # Load the best model for final testing
    if os.path.exists(args.output_model_path):
        state_dict = torch.load(args.output_model_path, map_location=args.device)
        model.load_state_dict(state_dict)
    else:
        print("Warning: Best model file not found. Evaluating the last state of the model.")

    print("\n***** Final evaluation on Test Set *****")
    evaluate(args, model, test_dataset, print_confusion_matrix=True)


if __name__ == "__main__":
    main()