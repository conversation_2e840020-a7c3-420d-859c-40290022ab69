# VPN通道数据集预处理报告

## 📊 处理概览

- **输入目录**: D:\VPN通道识别\YaTC-main\data\VPN_MFR
- **输出目录**: D:\VPN通道识别\YaTC-main\data\VPN_MFR_balanced
- **处理时间**: None

## 📈 原始数据集统计

- **总类别数**: 14
- **总样本数**: 4354
- **每类最少样本**: 2
- **每类最多样本**: 1200
- **每类平均样本**: 311.0
- **样本数标准差**: 371.3

### 原始类别分布
| 类别 | 样本数 |
|------|--------|
| CiscoVPN | 655 |
| Clash_DoH | 1200 |
| IPSec | 737 |
| L2TP | 300 |
| OpenVPN | 668 |
| OpenVPN_Vless | 2 |
| OpenVPN_Vmess | 4 |
| SSLVPN | 218 |
| SSR_Vmess | 2 |
| SS_Vmess | 3 |
| Vless | 10 |
| Vmess | 15 |
| Wireguard | 537 |
| Wireguard_SSH | 3 |

## 🎯 平衡后数据集统计

- **总样本数**: 4854
- **每类最少样本**: 100
- **每类最多样本**: 860
- **每类平均样本**: 346.7
- **样本数标准差**: 268.4

### 平衡后类别分布
| 类别 | 样本数 | 变化 |
|------|--------|------|
| CiscoVPN | 655 | 0 |
| Clash_DoH | 860 | -340 |
| IPSec | 722 | -15 |
| L2TP | 310 | +10 |
| OpenVPN | 668 | 0 |
| OpenVPN_Vless | 100 | +98 |
| OpenVPN_Vmess | 150 | +146 |
| SSLVPN | 252 | +34 |
| SSR_Vmess | 100 | +98 |
| SS_Vmess | 100 | +97 |
| Vless | 150 | +140 |
| Vmess | 150 | +135 |
| Wireguard | 537 | 0 |
| Wireguard_SSH | 100 | +97 |

## ⚖️ 类别权重

| 类别 | 权重 |
|------|------|
| CiscoVPN | 0.4748 |
| Clash_DoH | 0.2592 |
| IPSec | 0.4220 |
| L2TP | 1.0367 |
| OpenVPN | 0.4656 |
| OpenVPN_Vless | 155.5000 |
| OpenVPN_Vmess | 77.7500 |
| SSLVPN | 1.4266 |
| SSR_Vmess | 155.5000 |
| SS_Vmess | 103.6667 |
| Vless | 31.1000 |
| Vmess | 20.7333 |
| Wireguard | 0.5791 |
| Wireguard_SSH | 103.6667 |

## 🔧 数据增强配置

- **旋转角度范围**: ±15°
- **亮度调整范围**: (0.8, 1.2)
- **对比度调整范围**: (0.8, 1.2)
- **噪声标准差**: 0.02
- **模糊半径**: 0.5
- **水平翻转**: 是
- **垂直翻转**: 是

## 📝 使用说明

### 1. 使用平衡后的数据集
平衡后的数据集位于: `D:\VPN通道识别\YaTC-main\data\VPN_MFR_balanced`

### 2. 使用类别权重
类别权重配置文件: `class_weights.json`

在YaTC训练中使用:
```python
import json
with open('class_weights.json', 'r') as f:
    config = json.load(f)
class_weights = torch.tensor(config['weights_array'])
```

### 3. 验证增强质量
查看验证样本: `validation_samples/`目录

---
*报告生成时间: 2025-08-05 20:43:00*
