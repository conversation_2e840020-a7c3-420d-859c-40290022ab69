#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
VPN通道分类器训练脚本
使用ET-BERT模型对14个VPN通道类别进行分类（已移除标签8和11，移除香农熵特征）
"""

import os
import sys
import subprocess
import argparse

def check_files_exist():
    """检查必要的文件是否存在"""
    required_files = [
        "ET-BERT-main/models/pre-trained_model.bin",
        "ET-BERT-main/models/encryptd_vocab.txt",
        "ET-BERT-main/results/vpn_tunnels_class14/train_dataset_14class.tsv",
        "ET-BERT-main/results/vpn_tunnels_class14/dev_dataset_14class.tsv",
        "ET-BERT-main/results/vpn_tunnels_class14/test_dataset_14class.tsv"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("错误：以下必要文件缺失：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✓ 所有必要文件都存在")
    return True

def run_training(epochs=10, batch_size=8, learning_rate=5e-6, seq_length=256):
    """运行ET-BERT训练"""
    
    print("=" * 60)
    print(f"训练参数:")
    print(f"  - 类别数: 14 (已移除标签8和11，移除香农熵特征)")
    print(f"  - 训练轮数: {epochs}")
    print(f"  - 批次大小: {batch_size}")
    print(f"  - 学习率: {learning_rate}")
    print(f"  - 序列长度: {seq_length}")
    print("=" * 60)
    
    # 构建训练命令
    cmd = [
        "python", "ET-BERT-main/fine-tuning/run_classifier.py",
        "--config_path", "ET-BERT-main/models/bert/base_config.json",
        "--pretrained_model_path", "ET-BERT-main/models/pre-trained_model.bin",
        "--vocab_path", "ET-BERT-main/models/encryptd_vocab.txt",
        "--train_path", "ET-BERT-main/results/vpn_tunnels_class14/train_dataset_14class.tsv",
        "--dev_path", "ET-BERT-main/results/vpn_tunnels_class14/dev_dataset_14class.tsv",
        "--test_path", "ET-BERT-main/results/vpn_tunnels_class14/test_dataset_14class.tsv",
        "--output_model_path", "ET-BERT-main/models/vpn_classifier_14class.bin",
        "--epochs_num", str(epochs),
        "--batch_size", str(batch_size),
        "--learning_rate", str(learning_rate),
        "--seq_length", str(seq_length),
        "--labels_num", "14",
        "--use_class_weights",  
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--pooling", "first",
        "--tokenizer", "bert"
    ]
    
    print("执行命令:")
    print(" ".join(cmd))
    print("\n" + "=" * 60)
    
    try:
        # 运行训练
        result = subprocess.run(cmd, cwd=".", capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n" + "=" * 60)
            print("✓ 训练完成！")
            print("=" * 60)
            print("模型保存位置: ET-BERT-main/models/vpn_classifier_14class.bin")
            return True
        else:
            print("\n" + "=" * 60)
            print("✗ 训练失败！")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"\n训练过程中出现异常: {e}")
        return False

def run_evaluation():
    """运行模型评估"""
    print("\n" + "=" * 60)
    print("开始模型评估")
    print("=" * 60)
    
    # 构建推理命令
    cmd = [
        "python", "ET-BERT-main/inference/run_classifier_infer.py",
        "--config_path", "ET-BERT-main/models/bert/base_config.json",
        "--load_model_path", "ET-BERT-main/models/vpn_classifier_14class.bin",
        "--vocab_path", "ET-BERT-main/models/encryptd_vocab.txt",
        "--test_path", "ET-BERT-main/results/vpn_tunnels_class14/test_dataset_14class.tsv",
        "--prediction_path", "ET-BERT-main/results/vpn_tunnels_class14/predictions.tsv",
        "--labels_num", "14",
        "--embedding", "word_pos_seg",
        "--encoder", "transformer",
        "--mask", "fully_visible",
        "--pooling", "first",
        "--tokenizer", "bert"
    ]
    
    print("执行评估命令:")
    print(" ".join(cmd))
    print()
    
    try:
        result = subprocess.run(cmd, cwd=".", capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n" + "=" * 60)
            print("✓ 评估完成！")
            print("=" * 60)
            print("预测结果保存位置: ET-BERT-main/results/vpn_tunnels_class14/predictions.tsv")
            return True
        else:
            print("\n" + "=" * 60)
            print("✗ 评估失败！")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"\n评估过程中出现异常: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="VPN通道分类器训练脚本")
    parser.add_argument("--epochs", type=int, default=10, help="训练轮数")
    parser.add_argument("--batch_size", type=int, default=8, help="批次大小")
    parser.add_argument("--learning_rate", type=float, default=1e-5, help="学习率")
    parser.add_argument("--seq_length", type=int, default=256, help="序列长度")
    parser.add_argument("--skip_check", action="store_true", help="跳过文件检查")
    parser.add_argument("--train_only", action="store_true", help="只训练，不评估")
    
    args = parser.parse_args()
    
    print("VPN通道分类器训练工具")
    print("=" * 60)
    
    # 检查文件
    if not args.skip_check:
        if not check_files_exist():
            print("请确保所有必要文件都存在后再运行训练")
            return
    
    # 运行训练
    success = run_training(
        epochs=args.epochs,
        batch_size=args.batch_size, 
        learning_rate=args.learning_rate,
        seq_length=args.seq_length
    )
    
    if not success:
        print("训练失败，退出")
        return
    
    # 运行评估（如果不是只训练模式）
    if not args.train_only:
        run_evaluation()
    
    print("\n" + "=" * 60)
    print("所有任务完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
