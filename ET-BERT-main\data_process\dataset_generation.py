#!/usr/bin/python3
#-*- coding:utf-8 -*-

import os
import sys
import copy
import xlrd
import json
import shutil
import pickle
import random
import binascii
import operator
import numpy as np
import pandas as pd
import scapy.all as scapy
from functools import reduce
import functools
from multiprocessing import Pool, cpu_count
from tqdm import tqdm

# This tool seems to be unavailable or not used in the main logic, so we wrap it in a try-except block.
try:
    from flowcontainer.extractor import extract
except ImportError:
    print("Warning: flowcontainer.extractor not found. Flow-based features will not be available.")
    extract = None

random.seed(40)

word_dir = "I:/corpora/"
word_name = "encrypted_burst.txt"

def convert_pcapng_2_pcap(pcapng_path, pcapng_file, output_path):
    
    pcap_file = output_path + pcapng_file.replace('pcapng','pcap')
    cmd = "\"C:\\Program Files\\Wireshark\\editcap.exe\" -F pcap %s %s"
    command = cmd%(pcapng_path+pcapng_file, pcap_file)
    os.system(command)
    return 0

def split_cap(pcap_path, pcap_file, pcap_name, pcap_label='', dataset_level = 'flow'):
    
    # Use os.path.join for robust path construction
    splitcap_base_dir = os.path.join(pcap_path, "splitcap")
    os.makedirs(splitcap_base_dir, exist_ok=True)

    if pcap_label:
        label_dir = os.path.join(splitcap_base_dir, pcap_label)
        os.makedirs(label_dir, exist_ok=True)
        output_path = os.path.join(label_dir, pcap_name)
        os.makedirs(output_path, exist_ok=True)
    else:
        output_path = os.path.join(splitcap_base_dir, pcap_name)
        os.makedirs(output_path, exist_ok=True)

    # Use a relative path for SplitCap.exe assuming it's in the project root
    splitcap_exe_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'SplitCap.exe'))
    if not os.path.exists(splitcap_exe_path):
        # Fallback to the original hardcoded path if not found in root
        splitcap_exe_path = "D:\\Engineer\\Yuhan\\ModeCode\\ET-BERT-main\\SplitCap.exe"

    if dataset_level == 'flow':
        cmd = f'"{splitcap_exe_path}" -r "{pcap_file}" -s session -o "{output_path}"'
    elif dataset_level == 'packet':
        cmd = f'"{splitcap_exe_path}" -r "{pcap_file}" -s packets 1 -o "{output_path}"'
    
    os.system(cmd)
    return output_path

def cut(obj, sec):
    result = [obj[i:i+sec] for i in range(0,len(obj),sec)]
    try:
        remanent_count = len(result[0])%4
    except Exception as e:
        remanent_count = 0
        print("cut datagram error!")
    if remanent_count == 0:
        pass
    else:
        result = [obj[i:i+sec+remanent_count] for i in range(0,len(obj),sec+remanent_count)]
    return result

def bigram_generation(packet_datagram, packet_len = 64, flag=True):
    result = ''
    generated_datagram = cut(packet_datagram,1)
    token_count = 0
    for sub_string_index in range(len(generated_datagram)):
        if sub_string_index != (len(generated_datagram) - 1):
            token_count += 1
            if token_count > packet_len:
                break
            else:
                merge_word_bigram = generated_datagram[sub_string_index] + generated_datagram[sub_string_index + 1]
        else:
            break
        result += merge_word_bigram
        result += ' '
    
    return result

def get_burst_feature(label_pcap, payload_len):
    if not extract:
        print("Error: flowcontainer.extractor is not available. Cannot get burst feature.")
        return 0
    feature_data = []
    
    packets = scapy.rdpcap(label_pcap)
    
    packet_direction = []
    feature_result = extract(label_pcap)
    for key in feature_result.keys():
        value = feature_result[key]
        packet_direction = [x // abs(x) for x in value.ip_lengths]

    if len(packet_direction) == len(packets):
        
        burst_data_string = ''
        
        burst_txt = ''
        
        for packet_index in range(len(packets)):
            packet_data = packets[packet_index].copy()
            data = (binascii.hexlify(bytes(packet_data)))
            
            packet_string = data.decode()[:2*payload_len]
            
            if packet_index == 0:
                burst_data_string += packet_string
            else:
                if packet_direction[packet_index] != packet_direction[packet_index - 1]:
                    
                    length = len(burst_data_string)
                    for string_txt in cut(burst_data_string, int(length / 2)):
                        burst_txt += bigram_generation(string_txt, packet_len=len(string_txt))
                        burst_txt += '\n'
                    burst_txt += '\n'
                    
                    burst_data_string = ''
                
                burst_data_string += packet_string
                if packet_index == len(packets) - 1:
                    
                    length = len(burst_data_string)
                    for string_txt in cut(burst_data_string, int(length / 2)):
                        burst_txt += bigram_generation(string_txt, packet_len=len(string_txt))
                        burst_txt += '\n'
                    burst_txt += '\n'
        
        with open(word_dir + word_name,'a') as f:
            f.write(burst_txt)
    return 0

# This is the worker function that will be executed in parallel
def worker_get_feature_packet(pcap_full_path, payload_len):
    """Wrapper for get_feature_packet to be used by multiprocessing pool."""
    try:
        feature_data = []
        packets = scapy.rdpcap(pcap_full_path)
        packet_data_string = ''
        MAX_LEN = 200000  # Define max length for feature string

        for packet in packets:
            packet_data = packet.copy()
            data = binascii.hexlify(bytes(packet_data))
            packet_string = data.decode()
            
            # Assuming TLS/TCP/IP header, payload starts after ~38 bytes (76 hex chars)
            new_packet_string = packet_string[76:]
            packet_data_string += bigram_generation(new_packet_string, packet_len=payload_len, flag=True)

            # --- OPTIMIZATION: Early exit if max length is reached ---
            if len(packet_data_string) >= MAX_LEN:
                break

        # Truncate the feature string to the exact maximum length
        packet_data_string = packet_data_string[:MAX_LEN]

        feature_data.append(packet_data_string)
        return feature_data
    except Exception as e:
        print(f"Error processing file {pcap_full_path}: {e}")
        return None


def get_feature_flow(label_pcap, payload_len, payload_pac):
    if not extract:
        print("Error: flowcontainer.extractor is not available. Cannot get flow feature.")
        return -1
    
    feature_data = []
    packets = scapy.rdpcap(label_pcap)
    packet_count = 0  
    flow_data_string = '' 

    feature_result = extract(label_pcap, filter='tcp', extension=['tls.record.content_type', 'tls.record.opaque_type', 'tls.handshake.type'])
    if len(feature_result) == 0:
        feature_result = extract(label_pcap, filter='udp')
        if len(feature_result) == 0:
            return -1
        extract_keys = list(feature_result.keys())[0]
        if len(feature_result[label_pcap, extract_keys[1], extract_keys[2]].ip_lengths) < 3:
            print("preprocess flow %s but this flow has less than 3 packets." % label_pcap)
            return -1
    elif len(packets) < 3:
        print("preprocess flow %s but this flow has less than 3 packets." % label_pcap)
        return -1
    try:
        if len(feature_result[label_pcap, 'tcp', '0'].ip_lengths) < 3:
            print("preprocess flow %s but this flow has less than 3 packets." % label_pcap)
            return -1
    except Exception as e:
        print("*** this flow begings from 1 or other numbers than 0.")
        for key in feature_result.keys():
            if len(feature_result[key].ip_lengths) < 3:
                print("preprocess flow %s but this flow has less than 3 packets." % label_pcap)
                return -1

    if feature_result.keys() == {}.keys():
        return -1

    packet_length = []
    packet_time = []
    packet_direction = []
    packet_message_type = []
    
    if feature_result == {}:
        return -1
    feature_result_lens = len(feature_result.keys())
    for key in feature_result.keys():
        value = feature_result[key]
        packet_length.append(value.ip_lengths)
        packet_time.append(value.ip_timestamps)

        if len(packet_length) < feature_result_lens:
            continue
        elif len(packet_length) == 1:
            pass
        else:
            packet_length = [sum(packet_length, [])]
            packet_time = [sum(packet_time, [])]

        extension_dict = {}
        
        for len_index in range(len(packet_length)):
            extension_list = [0]*(len(packet_length[len_index]))

        extensions = value.extension
        
        if 'tls.record.content_type' in extensions.keys():
            for record_content in extensions['tls.record.content_type']:
                packet_index = record_content[1]
                ms_type = []
                
                if len(record_content[0]) > 2:
                    ms_type.extend(record_content[0].split(','))
                else:
                    ms_type.append(record_content[0])
                
                extension_dict[packet_index] = ms_type
            
            if 'tls.handshake.type' in extensions.keys():
                for tls_handshake in extensions['tls.handshake.type']:
                    packet_index = tls_handshake[1]
                    if packet_index not in extension_dict.keys():
                        continue
                    ms_type = []
                    if len(tls_handshake[0]) > 2:
                        ms_type.extend(tls_handshake[0].split(','))
                    else:
                        ms_type.append(tls_handshake[0])
                    source_length = len(extension_dict[packet_index])
                    for record_index in range(source_length):
                        if extension_dict[packet_index][record_index] == '22':
                            for handshake_type_index in range(len(ms_type)):
                                extension_dict[packet_index][record_index] = '22:' + ms_type[handshake_type_index]
                                if handshake_type_index > 0:
                                    extension_dict[packet_index].insert(handshake_type_index,
                                                                        ('22:' + ms_type[handshake_type_index]))
                            break
        if 'tls.record.opaque_type' in extensions.keys():
            for record_opaque in extensions['tls.record.opaque_type']:
                packet_index = record_opaque[1]
                ms_type = []
                if len(record_opaque[0]) > 2:
                    ms_type.extend(record_opaque[0].split(","))
                else:
                    ms_type.append(record_opaque[0])
                if packet_index not in extension_dict.keys():
                    extension_dict[packet_index] = ms_type
                else:
                    extension_dict[packet_index].extend(ms_type)

        extension_string_dict = {}
        for key in extension_dict.keys():
            temp_string = ''
            for status in extension_dict[key]:
                temp_string += status+','
            temp_string = temp_string[:-1]
            extension_string_dict[key] = temp_string
        
        is_source = 0
        if is_source:
            packet_message_type.append(extension_string_dict)
        else:
            for key in extension_dict.keys():
                if len(set(extension_dict[key])) == 1 and len(extension_dict[key]) > 1:
                    try:
                        extension_list[key] += len(extension_dict[key])
                    except Exception as e:
                        print(key)
                else:
                    for status in extension_dict[key]:
                        if ':' in status:
                            
                            extension_list[key - 1] += reduce(operator.mul, [int(x) for x in status.split(':')], 1)
                        else:
                           
                            if key <= len(packet_length[0]):
                                extension_list[key - 1] += int(status)
                            else:
                                with open("error_while_writin_record","a") as f:
                                    f.write(label_pcap + '\n')
                                continue
            packet_message_type.append(extension_list)
    for length in packet_length[0]:
        if length > 0:
            packet_direction.append(1)
        else:
            packet_direction.append(-1)

    packet_index = 0
    for packet in packets:
        packet_count += 1
        if packet_count == payload_pac:
            packet_data = packet.copy()
            data = (binascii.hexlify(bytes(packet_data)))
            packet_string = data.decode()[76:]
            flow_data_string += bigram_generation(packet_string, packet_len=payload_len, flag = True)
            break
        else:
            packet_data = packet.copy()
            data = (binascii.hexlify(bytes(packet_data)))
            packet_string = data.decode()[76:]
            flow_data_string += bigram_generation(packet_string, packet_len=payload_len, flag = True)
    feature_data.append(flow_data_string)
    feature_data.append(packet_length[0])
    feature_data.append(packet_time[0])
    feature_data.append(packet_direction)
    feature_data.append(packet_message_type[0])

    return feature_data

def generation(pcap_path, samples, features, splitcap = False, payload_length = 128, payload_packet = 5, dataset_level = "flow"):
    """
    Main function to generate dataset from pcap files.
    This version uses multiprocessing for acceleration.
    """
    
    # This function is now the main entry point for data generation.
    # It discovers labels, prepares tasks, runs them in parallel, and returns the results.
    
    # 1. Discover labels (subdirectories) and sort them for consistent order
    label_name_list = []
    for _, dirs, _ in os.walk(pcap_path):
        if dirs:
            label_name_list.extend(dirs)
        break # only top-level
    label_name_list.sort()
    
    if not label_name_list:
        print(f"Error: No subdirectories found in {pcap_path}. Cannot generate dataset.")
        return [], []

    X_payload_packet, Y_train = [], []
    label_sum = len(label_name_list)
    sample_counts = samples * label_sum if len(samples) == 1 else samples

    # 2. Loop through each label and process its files in parallel
    for i in range(label_sum):
        class_name = label_name_list[i]
        class_label = i
        sample_num = sample_counts[i]

        print(f'Processing class: {class_name} (label {class_label}, {i+1}/{label_sum})')
        label_pcap_path = os.path.join(pcap_path, class_name)
        
        try:
            all_files = [f for f in os.listdir(label_pcap_path) if os.path.isfile(os.path.join(label_pcap_path, f))]
        except FileNotFoundError:
            print(f"  -> Warning: Directory not found: {label_pcap_path}. Skipping.")
            continue

        if len(all_files) < sample_num:
            print(f"  -> Warning: Requested {sample_num} samples, but only found {len(all_files)}. Using all available.")
            pcap_file_list = all_files
        else:
            pcap_file_list = random.sample(all_files, sample_num)
        
        if not pcap_file_list:
            print(f"  -> Warning: No pcap files to process for '{class_name}'. Skipping.")
            continue

        pcap_full_path_list = [os.path.join(label_pcap_path, fname) for fname in pcap_file_list]

        worker_func = functools.partial(worker_get_feature_packet, payload_len=payload_length)
        
        # Reserve 2 CPUs for system stability, as per user's change.
        num_processes = max(1, cpu_count() - 2)
        
        processed_results = []
        with Pool(processes=num_processes) as pool:
            results_iterator = pool.imap_unordered(worker_func, pcap_full_path_list)
            processed_results = list(tqdm(results_iterator, total=len(pcap_full_path_list), desc=f"  -> Extracting from {class_name}"))

        valid_results_count = 0
        for result in processed_results:
            if result and result[0]:
                X_payload_packet.append(result[0])
                valid_results_count += 1
        
        Y_train.extend([class_label] * valid_results_count)
        print(f'  -> Finished. Extracted {valid_results_count} valid samples for class {class_name}.')

    return X_payload_packet, Y_train

def read_data_from_json(json_data, features, samples):
    X,Y = [], []
    ablation_flag = 0
    for feature_index in range(len(features)):
        x = []
        label_count = 0
        for label in json_data.keys():
            sample_num = json_data[label]["samples"]
            if X == []:
                if not ablation_flag:
                    y = [label] * sample_num
                    Y.append(y)
                else:
                    if sample_num > 1500:
                        y = [label] * 1500
                    else:
                        y = [label] * sample_num
                    Y.append(y)
            if samples[label_count] < sample_num:
                x_label = []
                for sample_index in random.sample(list(json_data[label][features[feature_index]].keys()),1500):
                    x_label.append(json_data[label][features[feature_index]][sample_index])
                x.append(x_label)
            else:
                x_label = []
                for sample_index in json_data[label][features[feature_index]].keys():
                    x_label.append(json_data[label][features[feature_index]][sample_index])
                x.append(x_label)
            label_count += 1
        X.append(x)
    return X,Y

def obtain_data(pcap_path, samples, features, dataset_save_path, json_data = None):
    
    if json_data:
        X,Y = read_data_from_json(json_data,features,samples)
    else:
        print("read dataset from json file.")
        dataset_path = os.path.join(dataset_save_path, "dataset.json")
        with open(dataset_path,"r") as f:
            dataset = json.load(f)
        X,Y = read_data_from_json(dataset,features,samples)

    for index in range(len(X)):
        if len(X[index]) != len(Y[0]): # Fixed a bug here Y is not Y[0]
             print(f"Data and labels are not properly associated for feature {index}.")
             print(f"x: {len(X[index])}\ty: {len(Y[0])}")
             # This indicates a problem in data loading, but we let it pass for now.
    return X,Y


def combine_dataset_json():
    dataset_name = "I:\\traffic_pcap\\splitcap\\dataset-"
    # dataset vocab
    dataset = {}
    # progress
    progress_num = 8
    for i in range(progress_num):
        dataset_file = dataset_name + str(i) + ".json"
        with open(dataset_file,"r") as f:
            json_data = json.load(f)
        for key in json_data.keys():
            if i > 1:
                new_key = int(key) + 9*1 + 6*(i-1)
            else:
                new_key = int(key) + 9*i
            print(new_key)
            if new_key not in dataset.keys():
                dataset[new_key] = json_data[key]
    with open("I:\\traffic_pcap\\splitcap\\dataset.json","w") as f:
        json.dump(dataset, fp=f, ensure_ascii=False, indent=4)
    return 0

def pretrain_dataset_generation(pcap_path):
    output_split_path = "I:\\dataset\\"
    pcap_output_path = "I:\\dataset\\"
    
    if not os.listdir(pcap_output_path):
        print("Begin to convert pcapng to pcap.")
        for _parent,_dirs,files in os.walk(pcap_path):
            for file in files:
                if 'pcapng' in file:
                    #print(_parent + file)
                    convert_pcapng_2_pcap(_parent, file, pcap_output_path)
                else:
                    shutil.copy(_parent+"\\"+file, pcap_output_path+file)
    
    if not os.path.exists(output_split_path + "splitcap"):
        print("Begin to split pcap as session flows.")
        
        for _p,_d,files in os.walk(pcap_output_path):
            for file in files:
                split_cap(output_split_path,_p+file,file)
    print("Begin to generate burst dataset.")
    # burst sample
    for _p,_d,files in os.walk(output_split_path + "splitcap"):
        for file in files:
            get_burst_feature(_p+"\\"+file, payload_len=64)
    return 0

def size_format(size):
    # 'KB'
    file_size = '%.3f' % float(size/1000)
    return file_size

if __name__ == '__main__':
    # This main block is for standalone execution and contains hardcoded paths.
    # It's not used when called from data_process/main.py.
    # pretrain
    pcap_path = "I:\\pcaps\\"
    # tls 13 downstream
    #pcap_path, samples, features = "I:\\dataset\\labeled\\", 500, ["payload","length","time","direction","message_type"]
    #X,Y = generation(pcap_path, samples, features, splitcap=False)
    # pretrain data
    pretrain_dataset_generation(pcap_path)
    #print("X:%s\tx:%s\tY:%s"%(len(X),len(X[0]),len(Y)))
    # combine dataset.json
    #combine_dataset_json()