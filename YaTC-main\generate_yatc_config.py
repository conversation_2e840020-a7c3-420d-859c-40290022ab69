#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YaTC训练配置生成器
为平衡后的VPN通道数据集生成YaTC兼容的训练配置
"""

import json
import os
from pathlib import Path
import argparse

def generate_yatc_config(data_root, class_weights_file, output_file='yatc_config.py'):
    """
    生成YaTC训练配置文件
    
    Args:
        data_root: 平衡后的数据集根目录
        class_weights_file: 类别权重JSON文件路径
        output_file: 输出的配置文件名
    """
    
    # 读取类别权重配置
    with open(class_weights_file, 'r', encoding='utf-8') as f:
        weights_config = json.load(f)
    
    # 获取类别信息
    classes = sorted(weights_config['class_to_idx'].keys())
    num_classes = len(classes)
    class_weights = weights_config['weights_array']
    
    # 生成配置文件内容
    config_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YaTC VPN通道数据集训练配置
自动生成的配置文件，适配平衡后的VPN通道数据集
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torchvision import transforms
import os

# ==================== 数据集配置 ====================

# 数据集路径
DATA_ROOT = r"{data_root}"
TRAIN_DIR = os.path.join(DATA_ROOT, "train")
VAL_DIR = os.path.join(DATA_ROOT, "val") 
TEST_DIR = os.path.join(DATA_ROOT, "test")

# 类别配置
NUM_CLASSES = {num_classes}
CLASS_NAMES = {classes}

# 类别到索引的映射
CLASS_TO_IDX = {weights_config['class_to_idx']}
IDX_TO_CLASS = {weights_config['idx_to_class']}

# 类别权重 (用于处理数据不平衡)
CLASS_WEIGHTS = torch.tensor({class_weights}, dtype=torch.float32)

# ==================== 模型配置 ====================

# 输入图像尺寸 (MFR图像为40x40)
INPUT_SIZE = (40, 40)
INPUT_CHANNELS = 1  # 灰度图像

# 模型架构参数
MODEL_CONFIG = {{
    'input_size': INPUT_SIZE,
    'input_channels': INPUT_CHANNELS,
    'num_classes': NUM_CLASSES,
    'dropout_rate': 0.5,
    'use_batch_norm': True,
}}

# ==================== 训练配置 ====================

# 训练参数
BATCH_SIZE = 32
LEARNING_RATE = 0.001
NUM_EPOCHS = 100
WEIGHT_DECAY = 1e-4

# 优化器配置
OPTIMIZER_CONFIG = {{
    'type': 'Adam',
    'lr': LEARNING_RATE,
    'weight_decay': WEIGHT_DECAY,
    'betas': (0.9, 0.999),
}}

# 学习率调度器
SCHEDULER_CONFIG = {{
    'type': 'StepLR',
    'step_size': 30,
    'gamma': 0.1,
}}

# 损失函数配置 (使用加权交叉熵)
LOSS_CONFIG = {{
    'type': 'CrossEntropyLoss',
    'weight': CLASS_WEIGHTS,
    'label_smoothing': 0.1,
}}

# ==================== 数据增强配置 ====================

# 训练时的数据变换
TRAIN_TRANSFORMS = transforms.Compose([
    transforms.Grayscale(num_output_channels=1),
    transforms.Resize(INPUT_SIZE),
    transforms.RandomRotation(degrees=10),
    transforms.RandomHorizontalFlip(p=0.5),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5], std=[0.5]),
])

# 验证/测试时的数据变换
VAL_TRANSFORMS = transforms.Compose([
    transforms.Grayscale(num_output_channels=1),
    transforms.Resize(INPUT_SIZE),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.5], std=[0.5]),
])

# ==================== 训练监控配置 ====================

# 早停配置
EARLY_STOPPING_CONFIG = {{
    'patience': 15,
    'min_delta': 0.001,
    'monitor': 'val_loss',
    'mode': 'min',
}}

# 模型检查点配置
CHECKPOINT_CONFIG = {{
    'save_best_only': True,
    'monitor': 'val_accuracy',
    'mode': 'max',
    'save_dir': 'checkpoints',
}}

# 日志配置
LOGGING_CONFIG = {{
    'log_dir': 'logs',
    'log_interval': 10,  # 每10个batch记录一次
    'save_model_interval': 5,  # 每5个epoch保存一次模型
}}

# ==================== 评估配置 ====================

# 评估指标
METRICS = [
    'accuracy',
    'precision',
    'recall',
    'f1_score',
    'confusion_matrix',
]

# 类别平衡评估 (对于不平衡数据集很重要)
BALANCED_METRICS = {{
    'balanced_accuracy': True,
    'macro_f1': True,
    'weighted_f1': True,
    'per_class_metrics': True,
}}

# ==================== 设备配置 ====================

# 自动检测设备
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
NUM_WORKERS = 4  # 数据加载器的工作进程数

# ==================== 实用函数 ====================

def get_class_name(class_idx):
    """根据类别索引获取类别名称"""
    return IDX_TO_CLASS.get(str(class_idx), f"Unknown_{{class_idx}}")

def get_class_idx(class_name):
    """根据类别名称获取类别索引"""
    return CLASS_TO_IDX.get(class_name, -1)

def create_weighted_loss():
    """创建加权损失函数"""
    return nn.CrossEntropyLoss(
        weight=CLASS_WEIGHTS.to(DEVICE),
        label_smoothing=LOSS_CONFIG['label_smoothing']
    )

def print_config_summary():
    """打印配置摘要"""
    print("=" * 60)
    print("YaTC VPN通道数据集训练配置")
    print("=" * 60)
    print(f"数据集路径: {{DATA_ROOT}}")
    print(f"类别数量: {{NUM_CLASSES}}")
    print(f"输入尺寸: {{INPUT_SIZE}}")
    print(f"批次大小: {{BATCH_SIZE}}")
    print(f"学习率: {{LEARNING_RATE}}")
    print(f"训练轮数: {{NUM_EPOCHS}}")
    print(f"设备: {{DEVICE}}")
    print(f"使用类别权重: {{CLASS_WEIGHTS is not None}}")
    print("=" * 60)

# ==================== 数据加载器创建函数 ====================

def create_data_loaders():
    """创建数据加载器"""
    from torchvision.datasets import ImageFolder
    
    # 创建数据集
    train_dataset = ImageFolder(TRAIN_DIR, transform=TRAIN_TRANSFORMS)
    val_dataset = ImageFolder(VAL_DIR, transform=VAL_TRANSFORMS)
    test_dataset = ImageFolder(TEST_DIR, transform=VAL_TRANSFORMS)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE,
        shuffle=True,
        num_workers=NUM_WORKERS,
        pin_memory=True if DEVICE.type == 'cuda' else False
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=NUM_WORKERS,
        pin_memory=True if DEVICE.type == 'cuda' else False
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=NUM_WORKERS,
        pin_memory=True if DEVICE.type == 'cuda' else False
    )
    
    return train_loader, val_loader, test_loader

if __name__ == "__main__":
    print_config_summary()
'''
    
    # 保存配置文件
    output_path = Path(data_root).parent / output_file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"YaTC配置文件已生成: {output_path}")
    return output_path

def main():
    parser = argparse.ArgumentParser(description='生成YaTC训练配置文件')
    parser.add_argument('--data_root', type=str, required=True,
                       help='平衡后的数据集根目录')
    parser.add_argument('--weights_file', type=str, default='class_weights.json',
                       help='类别权重JSON文件路径')
    parser.add_argument('--output', type=str, default='yatc_config.py',
                       help='输出配置文件名')
    
    args = parser.parse_args()
    
    # 检查输入
    if not os.path.exists(args.data_root):
        print(f"错误: 数据集目录不存在 - {args.data_root}")
        return
    
    weights_path = Path(args.data_root) / args.weights_file
    if not weights_path.exists():
        print(f"错误: 权重文件不存在 - {weights_path}")
        return
    
    # 生成配置
    config_path = generate_yatc_config(args.data_root, weights_path, args.output)
    
    print("\n✅ 配置文件生成完成!")
    print(f"📁 配置文件: {config_path}")
    print("\n🎯 使用方法:")
    print("1. 将配置文件复制到YaTC项目目录")
    print("2. 在训练脚本中导入配置: import yatc_config")
    print("3. 使用配置进行训练")

if __name__ == "__main__":
    main()
