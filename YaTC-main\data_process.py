import os
import glob
import binascii
from PIL import Image
import scapy.all as scapy
from tqdm import tqdm
import numpy as np
from multiprocessing import Pool, cpu_count
from functools import partial

def makedir(path):
    try:
        os.mkdir(path)
    except Exception as E:
        pass

def read_5hp_list(pcap_dir):
    packets = scapy.rdpcap(pcap_dir)
    data = []
    for packet in packets:
        header = (binascii.hexlify(bytes(packet['IP']))).decode()
        try:
            payload = (binascii.hexlify(bytes(packet['Raw']))).decode()
            header = header.replace(payload, '')
        except:
            payload = ''
        if len(header) > 160:
            header = header[:160]
        elif len(header) < 160:
            header += '0' * (160 - len(header))
        if len(payload) > 480:
            payload = payload[:480]
        elif len(payload) < 480:
            payload += '0' * (480 - len(payload))
        data.append((header, payload))
        if len(data) >= 5:
            break
    if len(data) < 5:
        for i in range(5-len(data)):
            data.append(('0'*160, '0'*480))
    final_data = ''
    for h, p in data:
        final_data += h
        final_data += p
    return final_data

def process_pcap_to_png(filename, input_path_base, output_path_base):
    """
    Worker function to process a single pcap file and save it as a png.
    """
    try:
        content = read_5hp_list(filename)
        content = np.array([int(content[i:i + 2], 16) for i in range(0, len(content), 2)])
        fh = np.reshape(content, (40, 40))
        fh = np.uint8(fh)
        im = Image.fromarray(fh)
        # Construct the correct output path
        output_filename = filename.replace('.pcap', '.png').replace(input_path_base, output_path_base)
        im.save(output_filename)
        return True
    except Exception as e:
        # You can uncomment the line below for debugging if a file fails
        # print(f"Failed to process {filename}: {e}")
        return False

def MFR_generator(flows_pcap_path, output_path, specific_class=None):
    if specific_class:
        print(f"模式: 只处理单个类别 -> {specific_class}")
        class_input_path = os.path.join(flows_pcap_path, specific_class)
        flows = glob.glob(os.path.join(class_input_path, "*.pcap"))
        
        # Create output directories
        makedir(output_path)
        makedir(os.path.join(output_path, specific_class))
    else:
        print("模式: 处理所有类别")
        # Find all pcap files
        flows = glob.glob(flows_pcap_path + "/*/*.pcap")
        # Create output directories (this part is not parallel)
        makedir(output_path)
        classes = glob.glob(flows_pcap_path + "/*")
        print("正在创建所有输出目录...")
        for cla in tqdm(classes, desc="创建目录"):
            makedir(cla.replace(flows_pcap_path,output_path))

    if not flows:
        if specific_class:
            print(f"警告: 在目录 '{os.path.join(flows_pcap_path, specific_class)}' 中没有找到 .pcap 文件。")
        else:
            print(f"警告: 在目录 '{flows_pcap_path}/*/' 结构下没有找到 .pcap 文件。")
        return
        
    # Set up the worker function with fixed arguments
    worker_func = partial(process_pcap_to_png, 
                          input_path_base=flows_pcap_path, 
                          output_path_base=output_path)
    
    # Get number of available CPUs
    num_processes = cpu_count()-2
    print(f"\n找到 {len(flows)} 个 pcap 文件。使用 {num_processes} 个CPU核心开始并行处理...")
    
    # Create a pool and process files in parallel
    with Pool(num_processes) as pool:
        # Use tqdm to show progress for the parallel map
        results = list(tqdm(pool.imap_unordered(worker_func, flows), total=len(flows), desc="处理文件"))
    
    successful_files = sum(1 for r in results if r)
    print(f"\nParallel processing finished. {successful_files}/{len(flows)} files converted successfully.")

if __name__ == '__main__':
    # 1. 请将这里的路径替换成您存放pcap文件的根目录
    # 使用正斜杠 / 或者双反斜杠 \\ 来避免路径问题
    pcap_input_path = "D:/新工程数据集" 
    
    # 2. 这里是处理后生成的 .png 图片的存放目录，可以自行指定
    mfr_output_path = "data"

    # 3. (可选) 如果只想处理单个类别，请在这里指定类别名称
    #    如果将此行设置为 single_class_to_process = None，脚本则会处理所有类别
    single_class_to_process = "木马流量"

    print(f"开始 MFR 图片生成...")
    print(f"pcap 输入目录: {pcap_input_path}")
    print(f"png 输出目录: {mfr_output_path}")

    MFR_generator(pcap_input_path, mfr_output_path, specific_class=single_class_to_process)

    print("MFR 图片生成完毕。")

