import os
import glob
import binascii
from PIL import Image
import scapy.all as scapy
from tqdm import tqdm
import numpy as np
from multiprocessing import Pool, cpu_count
from functools import partial
import logging
import time
from pathlib import Path
import gc  # 垃圾回收
import psutil  # 内存监控

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('data_process.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def makedir(path):
    """创建目录，如果目录已存在则跳过"""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"创建目录失败: {path}, 错误: {e}")
        return False

def validate_pcap_file(pcap_path):
    """验证pcap文件是否有效"""
    try:
        if not os.path.exists(pcap_path):
            return False, "文件不存在"

        if os.path.getsize(pcap_path) == 0:
            return False, "文件为空"

        # 尝试读取文件头部分包来验证文件格式
        packets = scapy.rdpcap(pcap_path, count=1)
        if len(packets) == 0:
            return False, "无法读取数据包"

        return True, "文件有效"
    except Exception as e:
        return False, f"文件验证失败: {str(e)}"

def read_5hp_list_optimized(pcap_dir):
    """
    优化版本：从pcap文件中提取前5个数据包的头部和载荷信息，生成MFR特征

    优化点：
    1. 使用流式读取，避免加载整个文件到内存
    2. 减少日志记录频率
    3. 显式内存清理
    4. 快速失败机制

    Args:
        pcap_dir: pcap文件路径

    Returns:
        final_data: 拼接后的十六进制字符串，用于生成MFR图像
    """
    packets = None
    try:
        # 快速文件检查（不使用scapy）
        if not os.path.exists(pcap_dir) or os.path.getsize(pcap_dir) == 0:
            return '0' * 3200

        # 使用count参数限制读取的包数量，避免加载整个文件
        # 读取最多10个包，确保能找到5个有效的IP包
        packets = scapy.rdpcap(pcap_dir, count=10)

        if not packets:
            return '0' * 3200

        data = []
        processed_packets = 0

        for packet in packets:
            try:
                # 检查是否包含IP层
                if not packet.haslayer('IP'):
                    continue

                header = (binascii.hexlify(bytes(packet['IP']))).decode()

                # 尝试提取载荷
                payload = ''
                try:
                    if packet.haslayer('Raw'):
                        payload = (binascii.hexlify(bytes(packet['Raw']))).decode()
                        # 从头部中移除载荷部分，避免重复
                        header = header.replace(payload, '')
                except:
                    pass  # 忽略载荷提取错误，使用空载荷

                # 标准化头部长度为160字符
                if len(header) > 160:
                    header = header[:160]
                elif len(header) < 160:
                    header += '0' * (160 - len(header))

                # 标准化载荷长度为480字符
                if len(payload) > 480:
                    payload = payload[:480]
                elif len(payload) < 480:
                    payload += '0' * (480 - len(payload))

                data.append((header, payload))
                processed_packets += 1

                # 找到5个有效包就停止
                if len(data) >= 5:
                    break

            except:
                continue  # 忽略单个包的处理错误

        # 如果数据包不足5个，用零填充
        while len(data) < 5:
            data.append(('0'*160, '0'*480))

        # 拼接所有数据
        final_data = ''
        for h, p in data:
            final_data += h
            final_data += p

        return final_data

    except Exception:
        # 静默处理错误，返回默认值
        return '0' * 3200
    finally:
        # 显式清理内存
        if packets is not None:
            del packets
        gc.collect()  # 强制垃圾回收

def process_pcap_to_png_optimized(filename, input_path_base, output_path_base):
    """
    优化版本：处理单个pcap文件并保存为png图像

    优化点：
    1. 减少日志记录
    2. 更快的错误处理
    3. 内存优化

    Args:
        filename: pcap文件路径
        input_path_base: 输入路径基础目录
        output_path_base: 输出路径基础目录

    Returns:
        bool: 处理是否成功
    """
    content = None
    content_array = None
    im = None

    try:
        # 提取MFR特征（使用优化版本）
        content = read_5hp_list_optimized(filename)

        # 快速验证内容长度
        if len(content) != 3200:
            return False

        # 将十六进制字符串转换为数值数组
        try:
            content_array = np.array([int(content[i:i + 2], 16) for i in range(0, len(content), 2)], dtype=np.uint8)
        except ValueError:
            return False

        # 重塑为40x40的矩阵
        fh = content_array.reshape(40, 40)

        # 创建PIL图像
        im = Image.fromarray(fh)

        # 构建输出文件路径
        output_filename = filename.replace('.pcap', '.png').replace(input_path_base, output_path_base)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_filename)
        os.makedirs(output_dir, exist_ok=True)

        # 保存图像
        im.save(output_filename)
        return True

    except Exception:
        return False
    finally:
        # 显式清理内存
        if content is not None:
            del content
        if content_array is not None:
            del content_array
        if im is not None:
            del im
        # 每处理一定数量的文件后进行垃圾回收
        if hash(filename) % 100 == 0:  # 每100个文件清理一次
            gc.collect()

def get_vpn_class_mapping():
    """
    获取VPN通道数据集的类别映射

    Returns:
        dict: 类别名称映射字典
    """
    return {
        'OpenVPN+Vless': 'OpenVPN_Vless',
        'OpenVPN+Vmess': 'OpenVPN_Vmess',
        'SS+Vmess': 'SS_Vmess',
        'SSLVPN': 'SSLVPN',
        'SSR+Vmess': 'SSR_Vmess',
        'Vless': 'Vless',
        'Vmess': 'Vmess',
        'Wireguard+SSH': 'Wireguard_SSH',
        'ciscovpn': 'CiscoVPN',
        'clash_doh': 'Clash_DoH',
        'ipsec': 'IPSec',
        'l2tp': 'L2TP',
        'openvpn': 'OpenVPN',
        'wireguard': 'Wireguard'
    }

def validate_vpn_dataset(flows_pcap_path):
    """
    验证VPN数据集的结构和完整性

    Args:
        flows_pcap_path: VPN数据集根目录

    Returns:
        tuple: (是否有效, 错误信息, 统计信息)
    """
    if not os.path.exists(flows_pcap_path):
        return False, f"数据集路径不存在: {flows_pcap_path}", {}

    class_mapping = get_vpn_class_mapping()
    expected_classes = set(class_mapping.keys())

    # 检查目录结构
    actual_classes = set()
    class_stats = {}

    for item in os.listdir(flows_pcap_path):
        item_path = os.path.join(flows_pcap_path, item)
        if os.path.isdir(item_path):
            actual_classes.add(item)
            # 统计每个类别的文件数量
            pcap_files = glob.glob(os.path.join(item_path, "*.pcap"))
            class_stats[item] = len(pcap_files)

    # 检查缺失的类别
    missing_classes = expected_classes - actual_classes
    extra_classes = actual_classes - expected_classes

    stats = {
        'total_classes': len(actual_classes),
        'expected_classes': len(expected_classes),
        'class_stats': class_stats,
        'missing_classes': list(missing_classes),
        'extra_classes': list(extra_classes),
        'total_files': sum(class_stats.values())
    }

    if missing_classes:
        return False, f"缺失类别: {missing_classes}", stats

    if stats['total_files'] == 0:
        return False, "未找到任何pcap文件", stats

    return True, "数据集验证通过", stats

def MFR_generator(flows_pcap_path, output_path, specific_class=None):
    """
    生成MFR图像的主函数，适配VPN通道数据集

    Args:
        flows_pcap_path: 输入pcap文件根目录
        output_path: 输出png图像目录
        specific_class: 指定处理的类别，None表示处理所有类别
    """
    logger = setup_logging()

    # 验证VPN数据集
    logger.info("开始验证VPN数据集...")
    is_valid, msg, stats = validate_vpn_dataset(flows_pcap_path)

    if not is_valid:
        logger.error(f"数据集验证失败: {msg}")
        return

    logger.info(f"数据集验证成功: {msg}")
    logger.info(f"数据集统计: 总类别数={stats['total_classes']}, 总文件数={stats['total_files']}")

    # 获取类别映射
    class_mapping = get_vpn_class_mapping()

    if specific_class:
        logger.info(f"模式: 只处理单个类别 -> {specific_class}")
        if specific_class not in class_mapping:
            logger.error(f"未知的类别名称: {specific_class}")
            logger.info(f"可用类别: {list(class_mapping.keys())}")
            return

        class_input_path = os.path.join(flows_pcap_path, specific_class)
        flows = glob.glob(os.path.join(class_input_path, "*.pcap"))

        # 创建输出目录
        makedir(output_path)
        output_class_name = class_mapping[specific_class]
        makedir(os.path.join(output_path, output_class_name))

    else:
        logger.info("模式: 处理所有类别")
        # 查找所有pcap文件
        flows = glob.glob(flows_pcap_path + "/*/*.pcap")

        # 创建输出目录
        makedir(output_path)
        classes = glob.glob(flows_pcap_path + "/*")
        logger.info("正在创建所有输出目录...")

        for cla in tqdm(classes, desc="创建目录"):
            class_name = os.path.basename(cla)
            if class_name in class_mapping:
                output_class_name = class_mapping[class_name]
                output_class_path = os.path.join(output_path, output_class_name)
                makedir(output_class_path)
            else:
                logger.warning(f"跳过未知类别: {class_name}")

    if not flows:
        if specific_class:
            logger.warning(f"在目录 '{os.path.join(flows_pcap_path, specific_class)}' 中没有找到 .pcap 文件")
        else:
            logger.warning(f"在目录 '{flows_pcap_path}/*/' 结构下没有找到 .pcap 文件")
        return

    logger.info(f"找到 {len(flows)} 个 pcap 文件")

    # 设置工作函数（使用优化版本）
    worker_func = partial(process_pcap_to_png_with_mapping_optimized,
                          input_path_base=flows_pcap_path,
                          output_path_base=output_path,
                          class_mapping=class_mapping)

    # 获取CPU核心数（保守设置，避免过度并行导致内存问题）
    num_processes = max(1, min(cpu_count() - 1, 8))  # 最多使用8个进程
    batch_size = max(50, len(flows) // (num_processes * 4))  # 动态批次大小

    logger.info(f"使用 {num_processes} 个CPU核心，批次大小 {batch_size}")
    logger.info(f"开始处理 {len(flows)} 个文件...")

    # 记录初始内存使用
    initial_memory = monitor_memory_usage()
    logger.info(f"初始内存使用: {initial_memory:.1f} MB")

    # 分批并行处理文件
    start_time = time.time()
    all_results = []

    # 将文件分成批次
    for batch_start in range(0, len(flows), batch_size * num_processes):
        batch_end = min(batch_start + batch_size * num_processes, len(flows))
        current_batch = flows[batch_start:batch_end]

        batch_num = batch_start // (batch_size * num_processes) + 1
        total_batches = (len(flows) + batch_size * num_processes - 1) // (batch_size * num_processes)

        logger.info(f"处理批次 {batch_num}/{total_batches} ({len(current_batch)} 个文件)")

        # 记录批次开始内存
        batch_start_memory = monitor_memory_usage()

        # 并行处理当前批次
        with Pool(num_processes) as pool:
            batch_results = list(tqdm(
                pool.imap_unordered(worker_func, current_batch),
                total=len(current_batch),
                desc=f"批次 {batch_num}/{total_batches}"
            ))

        all_results.extend(batch_results)

        # 批次完成后的内存管理
        batch_end_memory = monitor_memory_usage()
        memory_increase = batch_end_memory - batch_start_memory

        if memory_increase > 100:  # 内存增长超过100MB
            logger.warning(f"批次 {batch_num} 内存增长: {memory_increase:.1f} MB")
            gc.collect()
            time.sleep(0.5)  # 给系统时间回收内存

        # 显示进度
        processed_so_far = len(all_results)
        success_so_far = sum(1 for r in all_results if r)
        logger.info(f"已处理: {processed_so_far}/{len(flows)}, 成功: {success_so_far}")

    # 统计最终结果
    successful_files = sum(1 for r in all_results if r)
    failed_files = len(flows) - successful_files
    processing_time = time.time() - start_time
    final_memory = monitor_memory_usage()

    logger.info(f"处理完成!")
    logger.info(f"成功处理: {successful_files}/{len(flows)} 个文件")
    logger.info(f"失败文件: {failed_files} 个")
    logger.info(f"处理时间: {processing_time:.2f} 秒")
    logger.info(f"平均速度: {len(flows)/processing_time:.2f} 文件/秒")
    logger.info(f"内存使用: {initial_memory:.1f} MB -> {final_memory:.1f} MB")

def process_pcap_to_png_with_mapping_optimized(filename, input_path_base, output_path_base, class_mapping):
    """
    优化版本：带类别映射的pcap到png转换函数

    优化点：
    1. 减少日志记录
    2. 更快的错误处理
    3. 内存优化和清理
    4. 批量垃圾回收

    Args:
        filename: pcap文件路径
        input_path_base: 输入路径基础目录
        output_path_base: 输出路径基础目录
        class_mapping: 类别名称映射字典

    Returns:
        bool: 处理是否成功
    """
    content = None
    content_array = None
    im = None

    try:
        # 提取类别名称
        relative_path = os.path.relpath(filename, input_path_base)
        original_class = relative_path.split(os.sep)[0]

        # 映射类别名称
        mapped_class = class_mapping.get(original_class, original_class)

        # 构建输出路径
        base_filename = os.path.basename(filename)
        output_filename = os.path.join(output_path_base, mapped_class,
                                     base_filename.replace('.pcap', '.png'))

        # 检查输出文件是否已存在（跳过重复处理）
        if os.path.exists(output_filename):
            return True

        # 提取MFR特征（使用优化版本）
        content = read_5hp_list_optimized(filename)

        # 快速验证内容长度
        if len(content) != 3200:
            return False

        # 转换为数值数组
        try:
            content_array = np.array([int(content[i:i + 2], 16) for i in range(0, len(content), 2)], dtype=np.uint8)
        except ValueError:
            return False

        # 重塑为40x40矩阵
        fh = content_array.reshape(40, 40)

        # 创建图像
        im = Image.fromarray(fh)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_filename)
        os.makedirs(output_dir, exist_ok=True)

        # 保存图像
        im.save(output_filename)
        return True

    except Exception:
        return False
    finally:
        # 显式清理内存
        if content is not None:
            del content
        if content_array is not None:
            del content_array
        if im is not None:
            del im
        # 定期垃圾回收
        if hash(filename) % 50 == 0:  # 每50个文件清理一次
            gc.collect()

def monitor_memory_usage():
    """监控内存使用情况"""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        return memory_mb
    except:
        return 0

def batch_process_with_memory_management(file_batch, worker_func, batch_size=100):
    """
    分批处理文件，包含内存管理

    Args:
        file_batch: 文件列表
        worker_func: 处理函数
        batch_size: 批处理大小

    Returns:
        list: 处理结果列表
    """
    results = []
    total_files = len(file_batch)

    for i in range(0, total_files, batch_size):
        batch = file_batch[i:i + batch_size]

        # 记录批次开始时的内存使用
        start_memory = monitor_memory_usage()

        # 处理当前批次
        batch_results = []
        for file_path in tqdm(batch, desc=f"批次 {i//batch_size + 1}"):
            result = worker_func(file_path)
            batch_results.append(result)

        results.extend(batch_results)

        # 强制垃圾回收
        gc.collect()

        # 记录批次结束时的内存使用
        end_memory = monitor_memory_usage()

        # 如果内存使用过高，额外清理
        if end_memory > start_memory * 1.5:  # 内存增长超过50%
            logging.warning(f"内存使用增长过快: {start_memory:.1f}MB -> {end_memory:.1f}MB")
            gc.collect()
            time.sleep(0.1)  # 短暂暂停让系统回收内存

    return results

if __name__ == '__main__':
    # ==================== VPN通道数据集配置 ====================

    # 1. VPN通道数据集根目录路径
    # 请根据实际情况修改此路径
    pcap_input_path = "D:/VPN通道识别/VPN通道数据集"

    # 2. 处理后生成的MFR图片存放目录
    mfr_output_path = "D:/VPN通道识别/YaTC-main/data/VPN_MFR"

    # 3. (可选) 指定处理单个类别，None表示处理所有类别
    # 可用类别: OpenVPN+Vless, OpenVPN+Vmess, SS+Vmess, SSLVPN, SSR+Vmess,
    #          Vless, Vmess, Wireguard+SSH, ciscovpn, clash_doh, ipsec, l2tp, openvpn, wireguard
    single_class_to_process = None  # 设置为None处理所有类别

    # ==================== 开始处理 ====================

    print("=" * 60)
    print("YaTC VPN通道数据集 MFR图像生成工具")
    print("=" * 60)
    print(f"输入目录: {pcap_input_path}")
    print(f"输出目录: {mfr_output_path}")

    if single_class_to_process:
        print(f"处理模式: 单类别 - {single_class_to_process}")
    else:
        print("处理模式: 所有类别")

    print("=" * 60)

    # 检查输入路径是否存在
    if not os.path.exists(pcap_input_path):
        print(f"错误: 输入路径不存在 - {pcap_input_path}")
        print("请检查路径设置并确保VPN通道数据集已正确放置")
        exit(1)

    try:
        # 开始MFR图像生成
        MFR_generator(pcap_input_path, mfr_output_path, specific_class=single_class_to_process)

        print("=" * 60)
        print("MFR图像生成完毕!")
        print(f"输出目录: {mfr_output_path}")
        print("=" * 60)

    except KeyboardInterrupt:
        print("\n用户中断处理过程")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        logging.error(f"主程序异常: {e}")

    print("程序结束")

