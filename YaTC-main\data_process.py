import os
import glob
import binascii
from PIL import Image
import scapy.all as scapy
from tqdm import tqdm
import numpy as np
from multiprocessing import Pool, cpu_count
from functools import partial
import logging
import time
from pathlib import Path

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('data_process.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def makedir(path):
    """创建目录，如果目录已存在则跳过"""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"创建目录失败: {path}, 错误: {e}")
        return False

def validate_pcap_file(pcap_path):
    """验证pcap文件是否有效"""
    try:
        if not os.path.exists(pcap_path):
            return False, "文件不存在"

        if os.path.getsize(pcap_path) == 0:
            return False, "文件为空"

        # 尝试读取文件头部分包来验证文件格式
        packets = scapy.rdpcap(pcap_path, count=1)
        if len(packets) == 0:
            return False, "无法读取数据包"

        return True, "文件有效"
    except Exception as e:
        return False, f"文件验证失败: {str(e)}"

def read_5hp_list(pcap_dir):
    """
    从pcap文件中提取前5个数据包的头部和载荷信息，生成MFR特征

    Args:
        pcap_dir: pcap文件路径

    Returns:
        final_data: 拼接后的十六进制字符串，用于生成MFR图像
    """
    try:
        # 验证文件
        is_valid, msg = validate_pcap_file(pcap_dir)
        if not is_valid:
            logging.warning(f"文件验证失败 {pcap_dir}: {msg}")
            # 返回全零数据作为默认值
            return '0' * 3200  # 5 * (160 + 480) = 3200

        packets = scapy.rdpcap(pcap_dir)
        data = []
        processed_packets = 0

        for packet in packets:
            try:
                # 检查是否包含IP层
                if not packet.haslayer('IP'):
                    continue

                header = (binascii.hexlify(bytes(packet['IP']))).decode()

                # 尝试提取载荷
                try:
                    if packet.haslayer('Raw'):
                        payload = (binascii.hexlify(bytes(packet['Raw']))).decode()
                        # 从头部中移除载荷部分，避免重复
                        header = header.replace(payload, '')
                    else:
                        payload = ''
                except Exception as e:
                    logging.debug(f"提取载荷失败 {pcap_dir}: {e}")
                    payload = ''

                # 标准化头部长度为160字符
                if len(header) > 160:
                    header = header[:160]
                elif len(header) < 160:
                    header += '0' * (160 - len(header))

                # 标准化载荷长度为480字符
                if len(payload) > 480:
                    payload = payload[:480]
                elif len(payload) < 480:
                    payload += '0' * (480 - len(payload))

                data.append((header, payload))
                processed_packets += 1

                if len(data) >= 5:
                    break

            except Exception as e:
                logging.debug(f"处理数据包失败 {pcap_dir}: {e}")
                continue

        # 如果数据包不足5个，用零填充
        if len(data) < 5:
            padding_needed = 5 - len(data)
            logging.debug(f"文件 {pcap_dir} 只有 {len(data)} 个有效数据包，需要填充 {padding_needed} 个")
            for _ in range(padding_needed):
                data.append(('0'*160, '0'*480))

        # 拼接所有数据
        final_data = ''
        for h, p in data:
            final_data += h
            final_data += p

        logging.debug(f"成功处理文件 {pcap_dir}，提取了 {processed_packets} 个数据包")
        return final_data

    except Exception as e:
        logging.error(f"读取pcap文件失败 {pcap_dir}: {e}")
        # 返回全零数据作为默认值
        return '0' * 3200

def process_pcap_to_png(filename, input_path_base, output_path_base):
    """
    处理单个pcap文件并保存为png图像

    Args:
        filename: pcap文件路径
        input_path_base: 输入路径基础目录
        output_path_base: 输出路径基础目录

    Returns:
        bool: 处理是否成功
    """
    try:
        # 提取MFR特征
        content = read_5hp_list(filename)

        # 验证内容长度
        if len(content) != 3200:
            logging.warning(f"文件 {filename} 的特征长度异常: {len(content)}, 期望: 3200")
            return False

        # 将十六进制字符串转换为数值数组
        try:
            content = np.array([int(content[i:i + 2], 16) for i in range(0, len(content), 2)])
        except ValueError as e:
            logging.error(f"十六进制转换失败 {filename}: {e}")
            return False

        # 重塑为40x40的矩阵 (YaTC的MFR格式)
        fh = np.reshape(content, (40, 40))
        fh = np.uint8(fh)

        # 创建PIL图像
        im = Image.fromarray(fh)

        # 构建输出文件路径
        output_filename = filename.replace('.pcap', '.png').replace(input_path_base, output_path_base)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_filename)
        if not makedir(output_dir):
            logging.error(f"无法创建输出目录: {output_dir}")
            return False

        # 保存图像
        im.save(output_filename)
        logging.debug(f"成功处理: {filename} -> {output_filename}")
        return True

    except Exception as e:
        logging.error(f"处理文件失败 {filename}: {e}")
        return False

def get_vpn_class_mapping():
    """
    获取VPN通道数据集的类别映射

    Returns:
        dict: 类别名称映射字典
    """
    return {
        'OpenVPN+Vless': 'OpenVPN_Vless',
        'OpenVPN+Vmess': 'OpenVPN_Vmess',
        'SS+Vmess': 'SS_Vmess',
        'SSLVPN': 'SSLVPN',
        'SSR+Vmess': 'SSR_Vmess',
        'Vless': 'Vless',
        'Vmess': 'Vmess',
        'Wireguard+SSH': 'Wireguard_SSH',
        'ciscovpn': 'CiscoVPN',
        'clash_doh': 'Clash_DoH',
        'ipsec': 'IPSec',
        'l2tp': 'L2TP',
        'openvpn': 'OpenVPN',
        'wireguard': 'Wireguard'
    }

def validate_vpn_dataset(flows_pcap_path):
    """
    验证VPN数据集的结构和完整性

    Args:
        flows_pcap_path: VPN数据集根目录

    Returns:
        tuple: (是否有效, 错误信息, 统计信息)
    """
    if not os.path.exists(flows_pcap_path):
        return False, f"数据集路径不存在: {flows_pcap_path}", {}

    class_mapping = get_vpn_class_mapping()
    expected_classes = set(class_mapping.keys())

    # 检查目录结构
    actual_classes = set()
    class_stats = {}

    for item in os.listdir(flows_pcap_path):
        item_path = os.path.join(flows_pcap_path, item)
        if os.path.isdir(item_path):
            actual_classes.add(item)
            # 统计每个类别的文件数量
            pcap_files = glob.glob(os.path.join(item_path, "*.pcap"))
            class_stats[item] = len(pcap_files)

    # 检查缺失的类别
    missing_classes = expected_classes - actual_classes
    extra_classes = actual_classes - expected_classes

    stats = {
        'total_classes': len(actual_classes),
        'expected_classes': len(expected_classes),
        'class_stats': class_stats,
        'missing_classes': list(missing_classes),
        'extra_classes': list(extra_classes),
        'total_files': sum(class_stats.values())
    }

    if missing_classes:
        return False, f"缺失类别: {missing_classes}", stats

    if stats['total_files'] == 0:
        return False, "未找到任何pcap文件", stats

    return True, "数据集验证通过", stats

def MFR_generator(flows_pcap_path, output_path, specific_class=None):
    """
    生成MFR图像的主函数，适配VPN通道数据集

    Args:
        flows_pcap_path: 输入pcap文件根目录
        output_path: 输出png图像目录
        specific_class: 指定处理的类别，None表示处理所有类别
    """
    logger = setup_logging()

    # 验证VPN数据集
    logger.info("开始验证VPN数据集...")
    is_valid, msg, stats = validate_vpn_dataset(flows_pcap_path)

    if not is_valid:
        logger.error(f"数据集验证失败: {msg}")
        return

    logger.info(f"数据集验证成功: {msg}")
    logger.info(f"数据集统计: 总类别数={stats['total_classes']}, 总文件数={stats['total_files']}")

    # 获取类别映射
    class_mapping = get_vpn_class_mapping()

    if specific_class:
        logger.info(f"模式: 只处理单个类别 -> {specific_class}")
        if specific_class not in class_mapping:
            logger.error(f"未知的类别名称: {specific_class}")
            logger.info(f"可用类别: {list(class_mapping.keys())}")
            return

        class_input_path = os.path.join(flows_pcap_path, specific_class)
        flows = glob.glob(os.path.join(class_input_path, "*.pcap"))

        # 创建输出目录
        makedir(output_path)
        output_class_name = class_mapping[specific_class]
        makedir(os.path.join(output_path, output_class_name))

    else:
        logger.info("模式: 处理所有类别")
        # 查找所有pcap文件
        flows = glob.glob(flows_pcap_path + "/*/*.pcap")

        # 创建输出目录
        makedir(output_path)
        classes = glob.glob(flows_pcap_path + "/*")
        logger.info("正在创建所有输出目录...")

        for cla in tqdm(classes, desc="创建目录"):
            class_name = os.path.basename(cla)
            if class_name in class_mapping:
                output_class_name = class_mapping[class_name]
                output_class_path = os.path.join(output_path, output_class_name)
                makedir(output_class_path)
            else:
                logger.warning(f"跳过未知类别: {class_name}")

    if not flows:
        if specific_class:
            logger.warning(f"在目录 '{os.path.join(flows_pcap_path, specific_class)}' 中没有找到 .pcap 文件")
        else:
            logger.warning(f"在目录 '{flows_pcap_path}/*/' 结构下没有找到 .pcap 文件")
        return

    logger.info(f"找到 {len(flows)} 个 pcap 文件")

    # 设置工作函数
    worker_func = partial(process_pcap_to_png_with_mapping,
                          input_path_base=flows_pcap_path,
                          output_path_base=output_path,
                          class_mapping=class_mapping)

    # 获取CPU核心数
    num_processes = max(1, cpu_count() - 2)
    logger.info(f"使用 {num_processes} 个CPU核心开始并行处理...")

    # 并行处理文件
    start_time = time.time()
    with Pool(num_processes) as pool:
        results = list(tqdm(pool.imap_unordered(worker_func, flows),
                           total=len(flows), desc="处理文件"))

    # 统计结果
    successful_files = sum(1 for r in results if r)
    failed_files = len(flows) - successful_files
    processing_time = time.time() - start_time

    logger.info(f"并行处理完成!")
    logger.info(f"成功处理: {successful_files}/{len(flows)} 个文件")
    logger.info(f"失败文件: {failed_files} 个")
    logger.info(f"处理时间: {processing_time:.2f} 秒")
    logger.info(f"平均速度: {len(flows)/processing_time:.2f} 文件/秒")

def process_pcap_to_png_with_mapping(filename, input_path_base, output_path_base, class_mapping):
    """
    带类别映射的pcap到png转换函数

    Args:
        filename: pcap文件路径
        input_path_base: 输入路径基础目录
        output_path_base: 输出路径基础目录
        class_mapping: 类别名称映射字典

    Returns:
        bool: 处理是否成功
    """
    try:
        # 提取类别名称
        relative_path = os.path.relpath(filename, input_path_base)
        original_class = relative_path.split(os.sep)[0]

        # 映射类别名称
        if original_class in class_mapping:
            mapped_class = class_mapping[original_class]
        else:
            logging.warning(f"未知类别 {original_class}，使用原始名称")
            mapped_class = original_class

        # 构建输出路径
        base_filename = os.path.basename(filename)
        output_filename = os.path.join(output_path_base, mapped_class,
                                     base_filename.replace('.pcap', '.png'))

        # 提取MFR特征
        content = read_5hp_list(filename)

        # 验证内容长度
        if len(content) != 3200:
            logging.warning(f"文件 {filename} 的特征长度异常: {len(content)}, 期望: 3200")
            return False

        # 转换为数值数组
        try:
            content = np.array([int(content[i:i + 2], 16) for i in range(0, len(content), 2)])
        except ValueError as e:
            logging.error(f"十六进制转换失败 {filename}: {e}")
            return False

        # 重塑为40x40矩阵
        fh = np.reshape(content, (40, 40))
        fh = np.uint8(fh)

        # 创建图像
        im = Image.fromarray(fh)

        # 确保输出目录存在
        output_dir = os.path.dirname(output_filename)
        if not makedir(output_dir):
            logging.error(f"无法创建输出目录: {output_dir}")
            return False

        # 保存图像
        im.save(output_filename)
        return True

    except Exception as e:
        logging.error(f"处理文件失败 {filename}: {e}")
        return False

if __name__ == '__main__':
    # ==================== VPN通道数据集配置 ====================

    # 1. VPN通道数据集根目录路径
    # 请根据实际情况修改此路径
    pcap_input_path = "VPN通道数据集"

    # 2. 处理后生成的MFR图片存放目录
    mfr_output_path = "data/VPN_MFR"

    # 3. (可选) 指定处理单个类别，None表示处理所有类别
    # 可用类别: OpenVPN+Vless, OpenVPN+Vmess, SS+Vmess, SSLVPN, SSR+Vmess,
    #          Vless, Vmess, Wireguard+SSH, ciscovpn, clash_doh, ipsec, l2tp, openvpn, wireguard
    single_class_to_process = None  # 设置为None处理所有类别

    # ==================== 开始处理 ====================

    print("=" * 60)
    print("YaTC VPN通道数据集 MFR图像生成工具")
    print("=" * 60)
    print(f"输入目录: {pcap_input_path}")
    print(f"输出目录: {mfr_output_path}")

    if single_class_to_process:
        print(f"处理模式: 单类别 - {single_class_to_process}")
    else:
        print("处理模式: 所有类别")

    print("=" * 60)

    # 检查输入路径是否存在
    if not os.path.exists(pcap_input_path):
        print(f"错误: 输入路径不存在 - {pcap_input_path}")
        print("请检查路径设置并确保VPN通道数据集已正确放置")
        exit(1)

    try:
        # 开始MFR图像生成
        MFR_generator(pcap_input_path, mfr_output_path, specific_class=single_class_to_process)

        print("=" * 60)
        print("MFR图像生成完毕!")
        print(f"输出目录: {mfr_output_path}")
        print("=" * 60)

    except KeyboardInterrupt:
        print("\n用户中断处理过程")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        logging.error(f"主程序异常: {e}")

    print("程序结束")

