#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
少数类别数据增强脚本
专门处理样本数少的VPN类别，将新样本追加到现有TSV文件中
"""

import os
import sys
import json
import random
import csv
import gc
import time
import subprocess
import tempfile
import shutil
import multiprocessing
import psutil  # 用于内存监控
import concurrent.futures
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm  # 进度条
import math
from collections import Counter

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data_process'))

# 导入数据处理模块
try:
    from data_process.dataset_generation import generation, bigram_generation
except ImportError:
    import data_process.dataset_generation as dataset_generation
    generation = dataset_generation.generation
    bigram_generation = dataset_generation.bigram_generation

# 尝试导入scapy作为备用方案
try:
    import scapy.all as scapy
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False

# VPN类别映射 - 只处理容易混淆的类别 (类别名 -> 标签索引)
MINORITY_VPN_CATEGORIES = {
    'Wireguard+SSR': 8
}

# 每个类别处理的文件数量 (设为None表示处理全部文件)
FILES_PER_CATEGORY = None

def calculate_shannon_entropy(data):
    """计算数据的香农熵"""
    if not data:
        return 0.0

    # 计算字节频率
    byte_counts = Counter(data)
    data_len = len(data)

    # 计算熵
    entropy = 0.0
    for count in byte_counts.values():
        probability = count / data_len
        if probability > 0:
            entropy -= probability * math.log2(probability)

    return entropy

def extract_enhanced_features_with_tshark(pcap_file, max_packets=5, timeout=30, debug=False):
    """使用tshark提取增强特征：payload + 包长度 + 香农熵"""
    try:
        # 提取payload数据和包长度
        cmd_payload = [
            'tshark', '-r', pcap_file, '-T', 'fields', '-e', 'data',
            '-Y', 'data', '-c', str(max_packets)
        ]

        cmd_length = [
            'tshark', '-r', pcap_file, '-T', 'fields', '-e', 'frame.len',
            '-c', str(max_packets)
        ]

        # 执行payload提取
        result_payload = subprocess.run(cmd_payload, capture_output=True, text=True, timeout=timeout)
        result_length = subprocess.run(cmd_length, capture_output=True, text=True, timeout=timeout)

        if debug:
            print(f"    tshark payload返回码: {result_payload.returncode}")
            print(f"    tshark length返回码: {result_length.returncode}")

        if (result_payload.returncode == 0 and result_payload.stdout.strip() and
            result_length.returncode == 0 and result_length.stdout.strip()):

            # 处理payload数据
            hex_lines = [line.strip() for line in result_payload.stdout.strip().split('\n') if line.strip()]
            length_lines = [line.strip() for line in result_length.stdout.strip().split('\n') if line.strip()]

            if not hex_lines or not length_lines:
                return None

            # 提取包长度特征
            packet_lengths = []
            for length_line in length_lines[:max_packets]:
                try:
                    packet_lengths.append(int(length_line))
                except ValueError:
                    continue

            # 处理每个包的payload并计算熵
            packet_entropies = []
            combined_hex_data = ""

            for hex_line in hex_lines[:max_packets]:
                hex_data = hex_line.replace(':', '').replace(' ', '')
                if len(hex_data) >= 4:  # 至少2个字节
                    combined_hex_data += hex_data

                    # 将hex转换为字节计算熵
                    try:
                        byte_data = bytes.fromhex(hex_data)
                        entropy = calculate_shannon_entropy(byte_data)
                        packet_entropies.append(entropy)
                    except ValueError:
                        packet_entropies.append(0.0)

            if len(combined_hex_data) >= 20:  # 至少10个字节
                # 生成原始bigram特征
                bigram_result = bigram_generation(combined_hex_data)

                # 构建增强特征字符串
                # 格式: bigram_features + " [LENGTHS:" + lengths + "] [ENTROPIES:" + entropies + "]"
                length_str = ",".join(map(str, packet_lengths[:5]))  # 最多5个包长度
                entropy_str = ",".join([f"{e:.3f}" for e in packet_entropies[:5]])  # 最多5个熵值

                enhanced_features = f"{bigram_result} [LENGTHS:{length_str}] [ENTROPIES:{entropy_str}]"

                if debug:
                    print(f"    包长度: {length_str}")
                    print(f"    熵值: {entropy_str}")
                    print(f"    增强特征长度: {len(enhanced_features)}")

                return enhanced_features
            elif debug:
                print(f"    hex数据太短: {len(combined_hex_data)} < 20")
        elif debug:
            print(f"    tshark失败或无输出")

        return None

    except Exception as e:
        if debug:
            print(f"    tshark异常: {e}")
        return None

def extract_payload_with_tshark(pcap_file, max_packets=5, timeout=30, debug=False):
    """使用tshark提取payload数据（保持向后兼容）"""
    try:
        cmd = [
            'tshark', '-r', pcap_file, '-T', 'fields', '-e', 'data',
            '-Y', 'data', '-c', str(max_packets)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)

        if debug:
            print(f"    tshark返回码: {result.returncode}")
            print(f"    tshark输出长度: {len(result.stdout) if result.stdout else 0}")
            if result.stderr:
                print(f"    tshark错误: {result.stderr[:100]}")

        if result.returncode == 0 and result.stdout.strip():
            hex_data = result.stdout.strip().replace('\n', '').replace(':', '')
            if debug:
                print(f"    提取的hex数据长度: {len(hex_data)}")

            if len(hex_data) >= 20:  # 至少10个字节
                bigram_result = bigram_generation(hex_data)
                if debug:
                    print(f"    bigram生成结果长度: {len(bigram_result) if bigram_result else 0}")
                return bigram_result
            elif debug:
                print(f"    hex数据太短: {len(hex_data)} < 20")
        elif debug:
            print(f"    tshark失败或无输出")

        return None

    except Exception as e:
        if debug:
            print(f"    tshark异常: {e}")
        return None

def extract_enhanced_features_with_scapy(pcap_file, max_packets=5):
    """使用scapy提取增强特征：payload + 包长度 + 香农熵（备用方案）"""
    try:
        packets = scapy.rdpcap(pcap_file)
        hex_data = ""
        packet_lengths = []
        packet_entropies = []

        count = 0
        for packet in packets:
            if count >= max_packets:
                break

            # 提取包长度
            packet_lengths.append(len(packet))

            if packet.haslayer(scapy.Raw):
                payload = packet[scapy.Raw].load
                hex_data += payload.hex()

                # 计算该包payload的熵
                entropy = calculate_shannon_entropy(payload)
                packet_entropies.append(entropy)
                count += 1
            else:
                # 如果没有Raw层，熵设为0
                packet_entropies.append(0.0)

        if len(hex_data) >= 20:
            # 生成原始bigram特征
            bigram_result = bigram_generation(hex_data)

            # 构建增强特征字符串
            length_str = ",".join(map(str, packet_lengths[:5]))
            entropy_str = ",".join([f"{e:.3f}" for e in packet_entropies[:5]])

            enhanced_features = f"{bigram_result} [LENGTHS:{length_str}] [ENTROPIES:{entropy_str}]"
            return enhanced_features
        else:
            return None

    except Exception as e:
        return None

def extract_payload_with_scapy(pcap_file, max_packets=5):
    """使用scapy提取payload数据（备用方案，保持向后兼容）"""
    try:
        packets = scapy.rdpcap(pcap_file)
        hex_data = ""

        count = 0
        for packet in packets:
            if count >= max_packets:
                break
            if packet.haslayer(scapy.Raw):
                hex_data += packet[scapy.Raw].load.hex()
                count += 1

        if len(hex_data) >= 20:
            return bigram_generation(hex_data)
        else:
            return None

    except Exception as e:
        return None

def is_valid_pcap_file(pcap_path):
    """检查pcap文件是否有效"""
    try:
        # 检查文件大小
        file_size = os.path.getsize(pcap_path)
        if file_size < 1024:  # 小于1KB的文件可能无效
            return False, f"文件太小 ({file_size} bytes)"

        if file_size > 2 * 1024 * 1024 * 1024:  # 大于2GB的文件可能导致问题
            return False, f"文件过大 ({file_size/1024/1024:.1f}MB)"

        # 检查pcap文件头
        with open(pcap_path, 'rb') as f:
            header = f.read(24)  # 读取完整的pcap文件头
            if len(header) < 24:
                return False, "文件头不完整"

            # pcap文件的魔数
            magic = header[:4]
            if magic not in [b'\xd4\xc3\xb2\xa1', b'\xa1\xb2\xc3\xd4', b'\x0a\x0d\x0d\x0a']:
                return False, "不是有效的pcap文件格式"

            # 检查版本号（应该是2.4）
            if magic in [b'\xd4\xc3\xb2\xa1', b'\xa1\xb2\xc3\xd4']:
                # 传统pcap格式
                import struct
                if magic == b'\xd4\xc3\xb2\xa1':
                    # 小端序
                    version_major, version_minor = struct.unpack('<HH', header[4:8])
                else:
                    # 大端序
                    version_major, version_minor = struct.unpack('>HH', header[4:8])

                if version_major != 2 or version_minor != 4:
                    return False, f"不支持的pcap版本 {version_major}.{version_minor}"

        return True, "有效"
    except Exception as e:
        return False, f"检查失败: {e}"

def split_pcap_into_flows(pcap_path, output_dir):
    """使用SplitCap将pcap文件分割为流"""
    try:
        # 确保使用绝对路径
        pcap_path = os.path.abspath(pcap_path)
        output_dir = os.path.abspath(output_dir)

        # 检查输入文件是否存在
        if not os.path.exists(pcap_path):
            print(f"    输入文件不存在: {pcap_path}")
            return []

        # 检查文件是否有效
        is_valid, reason = is_valid_pcap_file(pcap_path)
        if not is_valid:
            return []  # 静默跳过无效文件

        # 使用tshark进行额外验证
        try:
            tshark_cmd = ['tshark', '-r', pcap_path, '-c', '1', '-T', 'fields', '-e', 'frame.number']
            result = subprocess.run(tshark_cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                return []  # 静默跳过tshark无法读取的文件
        except Exception:
            pass  # 继续尝试SplitCap
        
        # 检查文件大小，跳过超大文件
        file_size_mb = os.path.getsize(pcap_path) / (1024 * 1024)
        if file_size_mb > 5000:  # 超过5GB的文件才跳过
            return []

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # SplitCap命令：按会话分割
        script_dir = os.path.dirname(os.path.abspath(__file__))
        splitcap_exe = os.path.join(script_dir, 'SplitCap.exe')

        # 检查SplitCap是否存在
        if not os.path.exists(splitcap_exe):
            return []  # 静默返回
        
        # 创建一个临时的pcap文件副本，使用清理后的文件名
        original_name = os.path.basename(pcap_path)
        # 清理文件名：移除特殊字符，缩短长度
        clean_name = original_name.replace('@', '_').replace('+', '_').replace(' ', '_')
        if len(clean_name) > 50:
            # 保留前20个字符和后20个字符（包括.pcap）
            name_part = clean_name[:-5]  # 移除.pcap
            clean_name = name_part[:20] + "_" + name_part[-15:] + ".pcap"

        temp_pcap_name = f"temp_{int(time.time())}_{clean_name}"
        temp_pcap_path = os.path.join(output_dir, temp_pcap_name)

        try:
            # 复制文件到临时位置
            shutil.copy2(pcap_path, temp_pcap_path)

            # 尝试不同的分割策略
            # 首先尝试按会话分割
            cmd = f'"{splitcap_exe}" -r "{temp_pcap_name}" -s session -o "."'

            # 根据文件大小动态设置超时时间
            if file_size_mb < 100:
                timeout_seconds = 300  # 小文件：5分钟
            elif file_size_mb < 1000:
                timeout_seconds = int(file_size_mb * 3)  # 中等文件：3秒/MB
            else:
                timeout_seconds = int(file_size_mb * 5)  # 大文件：5秒/MB

            timeout_seconds = max(300, min(timeout_seconds, 1800))  # 最少5分钟，最多30分钟

            # 在输出目录中运行SplitCap（静默运行）
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                                  timeout=timeout_seconds, cwd=output_dir)

        finally:
            # 清理临时pcap文件
            try:
                if os.path.exists(temp_pcap_path):
                    os.remove(temp_pcap_path)
            except:
                pass

        # 详细的错误诊断
        if result.returncode != 0:
            print(f"    SplitCap失败，返回码: {result.returncode}")
            if result.stdout:
                print(f"    SplitCap输出: {result.stdout[:200]}")
            if result.stderr:
                print(f"    SplitCap错误: {result.stderr[:200]}")

            # 检查是否是已知的错误返回码
            if result.returncode == 3762504530:
                print(f"    检测到SplitCap内部异常，尝试备用分割方法...")
            else:
                print(f"    SplitCap返回非零退出码，尝试备用分割方法...")

            return try_flow_split(pcap_path, output_dir, splitcap_exe, temp_pcap_name)

        # 获取生成的流文件
        try:
            flow_files = [f for f in os.listdir(output_dir)
                         if f.endswith('.pcap') and f != temp_pcap_name]
            return [os.path.join(output_dir, f) for f in flow_files]
        except Exception:
            return []

    except subprocess.TimeoutExpired:
        # SplitCap超时，返回原始文件
        return [pcap_path]
    except Exception:
        # 其他异常，返回原始文件
        return [pcap_path]

def process_single_flow_with_label(args):
    """处理单个流文件并返回带标签的结果 - 用于并行处理"""
    flow_path, label_idx = args

    try:
        # 检查文件是否存在和可读
        if not os.path.exists(flow_path):
            return ("error", f"文件不存在: {flow_path}")

        # 检查文件大小，调整限制策略
        try:
            file_size = os.path.getsize(flow_path)
            if file_size > 1000 * 1024 * 1024:  # 提高到200MB限制
                return ("error", f"文件过大: {os.path.basename(flow_path)} ({file_size/1024/1024:.1f}MB)")
            if file_size == 0:  # 跳过空文件
                return ("error", f"空文件: {os.path.basename(flow_path)}")
            if file_size < 100:  # 跳过过小的文件
                return ("error", f"文件过小: {os.path.basename(flow_path)} ({file_size} bytes)")

            # 对于大文件给出警告但继续处理
            if file_size > 50 * 1024 * 1024:
                print(f"      处理大流文件: {os.path.basename(flow_path)} ({file_size/1024/1024:.1f}MB)")

        except Exception as e:
            return ("error", f"文件大小检查失败: {os.path.basename(flow_path)} - {e}")

        # 根据文件大小调整提取策略
        if file_size > 50 * 1024 * 1024:
            # 大文件只提取前3个包，减少处理时间
            max_packets = 5
            timeout = 60  # 增加超时时间
        else:
            max_packets = 5
            timeout = 30

        # 根据标签决定使用哪种特征提取方法
        if label_idx in [7, 8]:  # Wireguard+SSH 和 Wireguard+SSR 使用增强特征
            # 首先尝试tshark增强特征提取
            payload_data = extract_enhanced_features_with_tshark(flow_path, max_packets=max_packets, timeout=timeout)

            # 如果tshark失败，尝试scapy增强特征备用方案
            if not payload_data and SCAPY_AVAILABLE:
                payload_data = extract_enhanced_features_with_scapy(flow_path, max_packets=max_packets)
        else:
            # 其他类别使用原始特征提取
            # 首先尝试tshark快速提取
            payload_data = extract_payload_with_tshark(flow_path, max_packets=max_packets, timeout=timeout)

            # 如果tshark失败，尝试scapy备用方案
            if not payload_data and SCAPY_AVAILABLE:
                payload_data = extract_payload_with_scapy(flow_path, max_packets=max_packets)

        if payload_data:
            # 随机分配到train/dev/test (70%/15%/15%)
            rand = random.random()
            if rand < 0.7:
                dataset_type = 'train'
            elif rand < 0.85:
                dataset_type = 'dev'
            else:
                dataset_type = 'test'

            return (dataset_type, label_idx, payload_data)
        else:
            return ("error", f"无法提取payload: {os.path.basename(flow_path)}")

    except Exception as e:
        # 返回错误信息而不是静默处理
        return ("error", f"处理异常: {os.path.basename(flow_path)} - {e}")

def process_single_flow(flow_path):
    """处理单个流文件，提取前5个包的payload - 兼容性函数"""
    try:
        # 检查文件是否存在和可读
        if not os.path.exists(flow_path):
            return None

        # 检查文件大小，跳过异常大的流文件
        try:
            file_size = os.path.getsize(flow_path)
            if file_size > 50 * 1024 * 1024:  # 跳过超过50MB的流文件
                return None
            if file_size == 0:  # 跳过空文件
                return None
        except:
            return None

        # 首先尝试tshark快速提取
        payload_data = extract_payload_with_tshark(flow_path, max_packets=5)

        # 如果tshark失败，尝试scapy备用方案
        if not payload_data and SCAPY_AVAILABLE:
            payload_data = extract_payload_with_scapy(flow_path, max_packets=5)

        return payload_data

    except Exception:
        # 静默处理异常，避免进程崩溃
        return None

def split_by_tcp_streams(pcap_path, output_dir):
    """使用tshark按TCP流分割"""
    try:
        base_name = os.path.splitext(os.path.basename(pcap_path))[0]

        # 获取所有TCP流的ID
        cmd = ['tshark', '-r', pcap_path, '-T', 'fields', '-e', 'tcp.stream', '-Y', 'tcp']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode != 0 or not result.stdout.strip():
            print(f"    无法获取TCP流信息，回退到包数分割")
            return split_by_packet_count(pcap_path, output_dir)

        # 解析流ID
        stream_lines = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
        stream_ids = sorted(set(stream_lines))

        if not stream_ids:
            print(f"    未发现TCP流，回退到包数分割")
            return split_by_packet_count(pcap_path, output_dir)

        print(f"    发现 {len(stream_ids)} 个TCP流")

        # 为每个流创建单独的文件
        split_files = []
        # 根据流数量决定处理多少个流
        max_streams = len(stream_ids)  

        print(f"    将处理前 {max_streams} 个TCP流")

        for i, stream_id in enumerate(stream_ids[:max_streams]):
            output_file = os.path.join(output_dir, f"{base_name}_stream_{stream_id}.pcap")

            cmd = [
                'tshark', '-r', pcap_path, '-w', output_file,
                f'tcp.stream=={stream_id}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                if file_size > 100:  # 只保留有内容的文件
                    split_files.append(output_file)
                else:
                    os.remove(output_file)  # 删除空文件

        if split_files:
            print(f"    按TCP流分割成功，生成 {len(split_files)} 个流文件")
            return split_files
        else:
            print(f"    TCP流分割失败，回退到包数分割")
            return split_by_packet_count(pcap_path, output_dir)

    except Exception as e:
        print(f"    TCP流分割异常: {e}，回退到包数分割")
        return split_by_packet_count(pcap_path, output_dir)

def split_by_packet_count(pcap_path, output_dir, packets_per_file=1000):
    """按包数分割pcap文件（备用方案）"""
    try:
        # 使用tshark按包数分割
        base_name = os.path.splitext(os.path.basename(pcap_path))[0]

        # 创建多个小文件
        split_files = []
        for i in range(5):  # 创建5个文件，每个包含不同的包
            output_file = os.path.join(output_dir, f"{base_name}_part_{i}.pcap")
            start_packet = i * packets_per_file + 1
            end_packet = (i + 1) * packets_per_file

            cmd = [
                'tshark', '-r', pcap_path, '-w', output_file,
                f'frame.number>={start_packet}', f'frame.number<={end_packet}'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                if file_size > 100:  # 只保留有内容的文件
                    split_files.append(output_file)
                else:
                    os.remove(output_file)  # 删除空文件

        if split_files:
            print(f"    按包数分割成功，生成 {len(split_files)} 个文件")
            return split_files
        else:
            print(f"    按包数分割失败，直接处理原始文件")
            return [pcap_path]

    except Exception as e:
        print(f"    按包数分割异常: {e}")
        return [pcap_path]

def try_flow_split(pcap_path, output_dir, splitcap_exe, temp_pcap_name):
    """尝试按流分割作为备用方案"""
    try:
        # 尝试按流分割
        cmd = f'"{splitcap_exe}" -r "{temp_pcap_name}" -s flow -o "."'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=1800, cwd=output_dir)

        if result.returncode == 0:
            # 获取生成的流文件
            flow_files = [f for f in os.listdir(output_dir)
                         if f.endswith('.pcap') and f != temp_pcap_name]
            if flow_files:
                print(f"    按流分割成功，生成 {len(flow_files)} 个流文件")
                return [os.path.join(output_dir, f) for f in flow_files]

        # 如果按流分割也失败，尝试按时间分割
        print(f"    按流分割失败，尝试按时间分割...")
        cmd = f'"{splitcap_exe}" -r "{temp_pcap_name}" -s seconds 60 -o "."'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True,
                              timeout=1800, cwd=output_dir)

        if result.returncode == 0:
            flow_files = [f for f in os.listdir(output_dir)
                         if f.endswith('.pcap') and f != temp_pcap_name]
            if flow_files:
                print(f"    按时间分割成功，生成 {len(flow_files)} 个文件")
                return [os.path.join(output_dir, f) for f in flow_files]

        # 所有分割方法都失败，尝试按包数分割原始文件
        print(f"    所有分割方法都失败，尝试按包数分割原始文件")
        return split_by_packet_count(pcap_path, output_dir)

    except Exception as e:
        print(f"    备用分割失败: {e}")
        return [pcap_path]

def analyze_pcap_sessions(pcap_path):
    """分析pcap文件的会话信息"""
    try:
        # 使用最简单的命令检查文件
        cmd = ['tshark', '-r', pcap_path, '-c', '10']  # 只读前10个包
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            lines = result.stdout.strip().split('\n') if result.stdout else []
            packet_count = len([line for line in lines if line.strip() and not line.startswith('Running')])
            print(f"      文件可读，检测到至少 {packet_count} 个包")
            return packet_count if packet_count > 0 else 1000  # 假设有1000个包
        else:
            print(f"      tshark读取失败: {result.stderr[:100] if result.stderr else '未知错误'}")
            return 0

    except Exception as e:
        print(f"      分析异常: {e}")
        return 0

def get_streams_from_file(pcap_path, debug=True):
    """获取pcap文件中的所有TCP和UDP流ID"""
    try:
        if debug:
            print(f"      调试: 分析文件 {os.path.basename(pcap_path)}")

        # 首先检查文件基本信息和协议分布
        cmd_protocols = ['tshark', '-r', pcap_path, '-T', 'fields', '-e', 'frame.protocols', '-c', '20']
        result_protocols = subprocess.run(cmd_protocols, capture_output=True, text=True, timeout=30)

        if result_protocols.returncode != 0:
            if debug:
                print(f"      调试: 无法读取PCAP文件，错误: {result_protocols.stderr}")
            return []

        protocols_found = set()
        frame_count = 0
        for line in result_protocols.stdout.strip().split('\n'):
            if line.strip():
                frame_count += 1
                protocols_found.update(line.strip().split(':'))

        if debug:
            print(f"      调试: 文件包含 {frame_count} 个数据包")
            print(f"      调试: 发现协议: {sorted(protocols_found)}")

        if frame_count == 0:
            if debug:
                print(f"      调试: 文件为空或无有效数据包")
            return []

        # 首先尝试TCP流
        tcp_streams = []
        if 'tcp' in protocols_found:
            cmd_tcp = ['tshark', '-r', pcap_path, '-T', 'fields', '-e', 'tcp.stream', '-Y', 'tcp']
            result_tcp = subprocess.run(cmd_tcp, capture_output=True, text=True, timeout=60)

            if result_tcp.returncode == 0 and result_tcp.stdout.strip():
                stream_lines = [line.strip() for line in result_tcp.stdout.strip().split('\n') if line.strip()]
                tcp_streams = [(f"tcp_{stream_id}", 'tcp', stream_id) for stream_id in sorted(set(stream_lines))]
                if debug:
                    print(f"      调试: 发现 {len(tcp_streams)} 个TCP流")

        # 然后尝试UDP流（对于Wireguard等UDP协议）
        udp_streams = []
        if 'udp' in protocols_found:
            cmd_udp = ['tshark', '-r', pcap_path, '-T', 'fields', '-e', 'udp.stream', '-Y', 'udp']
            result_udp = subprocess.run(cmd_udp, capture_output=True, text=True, timeout=60)

            if result_udp.returncode == 0 and result_udp.stdout.strip():
                stream_lines = [line.strip() for line in result_udp.stdout.strip().split('\n') if line.strip()]
                udp_streams = [(f"udp_{stream_id}", 'udp', stream_id) for stream_id in sorted(set(stream_lines))]
                if debug:
                    print(f"      调试: 发现 {len(udp_streams)} 个UDP流")

        # 如果UDP流检测失败，尝试基于IP地址的会话分组
        if not tcp_streams and not udp_streams and 'udp' in protocols_found:
            if debug:
                print(f"      调试: UDP流检测失败，尝试基于IP会话分组")

            cmd_sessions = ['tshark', '-r', pcap_path, '-T', 'fields', '-e', 'ip.src', '-e', 'ip.dst', '-e', 'udp.srcport', '-e', 'udp.dstport', '-Y', 'udp']
            result_sessions = subprocess.run(cmd_sessions, capture_output=True, text=True, timeout=60)

            if result_sessions.returncode == 0 and result_sessions.stdout.strip():
                sessions = set()
                valid_lines = 0
                for line in result_sessions.stdout.strip().split('\n'):
                    parts = line.strip().split('\t')
                    if debug and valid_lines < 3:  # 只显示前3行用于调试
                        print(f"      调试: 解析行: {parts}")

                    if len(parts) >= 4 and parts[0] and parts[1] and parts[2] and parts[3]:
                        # 创建会话标识符：IP1:PORT1_IP2:PORT2（排序确保一致性）
                        try:
                            endpoint1 = f"{parts[0]}:{parts[2]}"
                            endpoint2 = f"{parts[1]}:{parts[3]}"
                            session_id = "_".join(sorted([endpoint1, endpoint2]))
                            sessions.add(session_id)
                            valid_lines += 1
                        except Exception as e:
                            if debug:
                                print(f"      调试: 解析会话失败: {e}")

                if debug:
                    print(f"      调试: 解析了 {valid_lines} 行，发现 {len(sessions)} 个唯一会话")
                    if sessions:
                        print(f"      调试: 会话示例: {list(sessions)[:3]}")

                if sessions:
                    session_streams = [(f"session_{i}", 'session', session_id) for i, session_id in enumerate(sorted(sessions))]
                    if debug:
                        print(f"      调试: 创建 {len(session_streams)} 个UDP会话流")
                    return session_streams
                else:
                    if debug:
                        print(f"      调试: 未能创建任何会话流")
            else:
                if debug:
                    print(f"      调试: tshark会话查询失败，返回码: {result_sessions.returncode}")
                    if result_sessions.stderr:
                        print(f"      调试: 错误信息: {result_sessions.stderr}")

        # 如果所有方法都失败，尝试简单的IP对话（忽略错误）
        if not tcp_streams and not udp_streams:
            if debug:
                print(f"      调试: 所有流检测失败，尝试简单IP对话（忽略PCAP错误）")

            # 使用-P选项忽略PCAP文件错误
            cmd_simple = ['tshark', '-r', pcap_path, '-P', '-T', 'fields', '-e', 'ip.src', '-e', 'ip.dst']
            result_simple = subprocess.run(cmd_simple, capture_output=True, text=True, timeout=60)

            if debug:
                print(f"      调试: 简单IP查询返回码: {result_simple.returncode}")
                if result_simple.stderr:
                    print(f"      调试: 警告信息: {result_simple.stderr[:200]}")

            # 即使有错误，只要有输出就尝试处理
            if result_simple.stdout.strip():
                conversations = set()
                valid_lines = 0
                for line in result_simple.stdout.strip().split('\n'):
                    parts = line.strip().split('\t')
                    if len(parts) >= 2 and parts[0] and parts[1]:
                        # 创建对话标识符（排序IP地址确保一致性）
                        conv_id = "_".join(sorted([parts[0], parts[1]]))
                        conversations.add(conv_id)
                        valid_lines += 1

                if debug:
                    print(f"      调试: 处理了 {valid_lines} 行，发现 {len(conversations)} 个对话")
                    if conversations:
                        print(f"      调试: 对话示例: {list(conversations)[:3]}")

                if conversations:
                    conv_streams = [(f"conv_{i}", 'conv', conv_id) for i, conv_id in enumerate(sorted(conversations))]
                    if debug:
                        print(f"      调试: 创建 {len(conv_streams)} 个IP对话流")
                    return conv_streams

            # 最后的备用方案：使用scapy直接读取
            if debug:
                print(f"      调试: tshark完全失败，尝试scapy备用方案")

            try:
                import scapy.all as scapy
                packets = scapy.rdpcap(pcap_path)

                conversations = set()
                for packet in packets:
                    if packet.haslayer(scapy.IP):
                        src_ip = packet[scapy.IP].src
                        dst_ip = packet[scapy.IP].dst
                        conv_id = "_".join(sorted([src_ip, dst_ip]))
                        conversations.add(conv_id)

                if conversations:
                    conv_streams = [(f"scapy_conv_{i}", 'conv', conv_id) for i, conv_id in enumerate(sorted(conversations))]
                    if debug:
                        print(f"      调试: scapy创建 {len(conv_streams)} 个IP对话流")
                    return conv_streams

            except Exception as e:
                if debug:
                    print(f"      调试: scapy也失败了: {e}")
                return []

        return tcp_streams + udp_streams

    except Exception as e:
        if debug:
            print(f"      调试: 异常 - {e}")
        return []

def extract_single_stream(args):
    """提取单个流 - 支持TCP、UDP、会话流和整个文件"""
    pcap_path, stream_info, output_dir, category = args
    stream_name, protocol, stream_id = stream_info

    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成流文件名
        base_name = os.path.splitext(os.path.basename(pcap_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_{stream_name}.pcap")

        # 根据协议类型构建不同的过滤器
        if protocol == 'tcp':
            filter_expr = f'tcp.stream=={stream_id}'
        elif protocol == 'udp':
            filter_expr = f'udp.stream=={stream_id}'
        elif protocol == 'session':
            # UDP会话过滤器：基于IP:PORT对
            endpoints = stream_id.split('_')
            if len(endpoints) == 2:
                # 解析IP:PORT
                try:
                    ip1, port1 = endpoints[0].rsplit(':', 1)
                    ip2, port2 = endpoints[1].rsplit(':', 1)
                    filter_expr = f'((ip.src=={ip1} and udp.srcport=={port1} and ip.dst=={ip2} and udp.dstport=={port2}) or (ip.src=={ip2} and udp.srcport=={port2} and ip.dst=={ip1} and udp.dstport=={port1}))'
                except ValueError:
                    return None
            else:
                return None
        elif protocol == 'conv':
            # 会话过滤器：基于IP对话
            ips = stream_id.split('_')
            if len(ips) == 2:
                filter_expr = f'(ip.addr=={ips[0]} and ip.addr=={ips[1]})'
            else:
                return None
        else:
            return None

        # 使用tshark提取流（添加-P选项忽略PCAP错误）
        cmd = [
            'tshark', '-r', pcap_path, '-P', '-w', output_file,
            filter_expr
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        # 如果有错误信息但仍然生成了文件，记录但继续
        if result.stderr and "cut short" in result.stderr:
            print(f"        警告: PCAP文件可能损坏，但继续处理: {os.path.basename(pcap_path)}")

        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"        生成流文件: {os.path.basename(output_file)} ({file_size} bytes)")

            if file_size > 100:  # 只保留有内容的文件
                return output_file
            else:
                print(f"        删除空文件: {os.path.basename(output_file)} (太小: {file_size} bytes)")
                os.remove(output_file)  # 删除空文件
                return None
        else:
            print(f"        流文件未生成: {os.path.basename(output_file)}")
            if result.stderr:
                print(f"        错误信息: {result.stderr[:200]}")
            return None

    except Exception:
        return None

def collect_all_stream_tasks(pcap_files, category):
    """收集所有流级分割任务（支持TCP、UDP和会话流）"""
    all_stream_tasks = []

    print(f"  收集所有流信息（TCP、UDP、会话）...")

    for i, pcap_file in enumerate(pcap_files):
        # 获取该文件的所有流（TCP、UDP、会话）
        streams = get_streams_from_file(pcap_file)

        if streams:
            tcp_count = len([s for s in streams if s[1] == 'tcp'])
            udp_count = len([s for s in streams if s[1] == 'udp'])
            session_count = len([s for s in streams if s[1] == 'session'])
            conv_count = len([s for s in streams if s[1] == 'conv'])

            print(f"    文件 {os.path.basename(pcap_file)}: 发现 {len(streams)} 个流 (TCP:{tcp_count}, UDP:{udp_count}, 会话:{session_count}, 对话:{conv_count})")

            # 为每个流创建分割任务
            system_temp_dir = tempfile.gettempdir()
            temp_flow_dir = os.path.join(system_temp_dir, f"minority_flows_{category}_{i}_{int(time.time())}")

            for stream_info in streams:
                all_stream_tasks.append((pcap_file, stream_info, temp_flow_dir, category))
        else:
            print(f"    文件 {os.path.basename(pcap_file)}: 未发现任何流")

    print(f"  总计收集到 {len(all_stream_tasks)} 个流级分割任务")
    return all_stream_tasks

def process_single_category(category, label_idx, base_path):
    """处理单个类别的所有文件 - 文件级并行分割 + 流级并行提取"""
    print(f"\n处理类别: {category} (标签: {label_idx})")

    category_path = os.path.join(base_path, category)
    if not os.path.exists(category_path):
        print(f"  错误：类别目录不存在: {category_path}")
        return []
    
    # 获取pcap文件列表并过滤
    all_pcap_files = [f for f in os.listdir(category_path) if f.endswith('.pcap')]
    if not all_pcap_files:
        print(f"  错误：类别 {category} 中没有找到pcap文件")
        return []

    # 过滤文件（静默处理，只报告错误）
    pcap_files = []
    skipped_count = 0
    for f in all_pcap_files:
        file_path = os.path.join(category_path, f)
        try:
            file_size = os.path.getsize(file_path)
            if file_size < 1024 or file_size > 1000 * 1024 * 1024:
                skipped_count += 1
                continue
        except:
            skipped_count += 1
            continue

        pcap_files.append(f)

    if skipped_count > 0:
        print(f"  跳过了 {skipped_count} 个无效文件")

    if not pcap_files:
        print(f"  错误：类别 {category} 中没有有效的pcap文件")
        return []

    # 限制文件数量（如果设置了限制）
    if FILES_PER_CATEGORY is not None and len(pcap_files) > FILES_PER_CATEGORY:
        pcap_files = pcap_files[:FILES_PER_CATEGORY]
        print(f"  处理 {len(pcap_files)} 个pcap文件 (限制为{FILES_PER_CATEGORY}个)")
    else:
        print(f"  处理全部 {len(pcap_files)} 个pcap文件")
    
    # 第一阶段：收集所有流级分割任务
    all_stream_tasks = collect_all_stream_tasks(
        [os.path.join(category_path, f) for f in pcap_files],
        category
    )

    if not all_stream_tasks:
        print(f"  错误：类别 {category} 没有发现任何TCP流")
        return []

    # 第二阶段：流级并行分割
    print(f"  阶段1: 流级并行分割 {len(all_stream_tasks)} 个TCP流...")

    # 设置流级并行度
    cpu_count = multiprocessing.cpu_count()
    stream_workers = min(max(1, int(cpu_count * 0.8)), len(all_stream_tasks), 32)  # 最多32个并行分割
    print(f"  使用 {stream_workers} 个进程并行分割TCP流")

    all_flow_files = []

    try:
        with ProcessPoolExecutor(max_workers=stream_workers) as executor:
            # 提交流级分割任务并使用进度条
            with tqdm(total=len(all_stream_tasks), desc=f"  分割{category}流", unit="流") as pbar:
                future_to_task = {
                    executor.submit(extract_single_stream, task): task
                    for task in all_stream_tasks
                }

                # 收集分割结果
                for future in as_completed(future_to_task):
                    try:
                        flow_file = future.result(timeout=120)  # 2分钟超时
                        if flow_file:
                            all_flow_files.append(flow_file)
                            pbar.set_postfix({"流文件": len(all_flow_files)})
                    except Exception as e:
                        task = future_to_task[future]
                        pcap_file, stream_id = task[0], task[1]
                        tqdm.write(f"    错误：分割流 {os.path.basename(pcap_file)}:{stream_id} 异常: {e}")

                    pbar.update(1)

    except Exception as e:
        print(f"  错误：流级并行分割异常: {e}")
        return []

    if not all_flow_files:
        print(f"  错误：类别 {category} 没有生成任何流文件")
        return []

    print(f"  阶段2: 并行提取 {len(all_flow_files)} 个流文件的特征...")

    # 流级并行处理 - 根据流文件数量调整并行度
    # 如果流文件很多，适当减少并行度以避免内存问题
    if len(all_flow_files) > 10000:
        flow_workers = min(max(1, int(cpu_count * 0.6)), 16)  # 大量文件时限制并行度
        print(f"  大量流文件({len(all_flow_files)}个)，使用保守并行度: {flow_workers}")
    elif len(all_flow_files) > 5000:
        flow_workers = min(max(1, int(cpu_count * 0.7)), 20)
        print(f"  中等流文件数量({len(all_flow_files)}个)，并行度: {flow_workers}")
    else:
        flow_workers = min(max(1, int(cpu_count * 0.8)), len(all_flow_files))
        print(f"  流文件数量适中({len(all_flow_files)}个)，并行度: {flow_workers}")

    all_samples = []

    try:
        # 根据流文件数量智能调整批次大小
        if len(all_flow_files) > 50000:
            batch_size = 3000  # 超大量文件，小批次
        elif len(all_flow_files) > 20000:
            batch_size = 5000  # 大量文件，中等批次
        elif len(all_flow_files) > 10000:
            batch_size = 8000  # 中等文件，大批次
        else:
            batch_size = len(all_flow_files)  # 少量文件，一次处理完

        for batch_start in range(0, len(all_flow_files), batch_size):
            batch_end = min(batch_start + batch_size, len(all_flow_files))
            batch_flow_files = all_flow_files[batch_start:batch_end]

            if len(all_flow_files) > batch_size:
                print(f"  处理批次 {batch_start//batch_size + 1}/{(len(all_flow_files)-1)//batch_size + 1}: {len(batch_flow_files)} 个流文件")

            with ProcessPoolExecutor(max_workers=flow_workers) as executor:
                # 准备任务参数
                flow_args = [(flow_file, label_idx) for flow_file in batch_flow_files]

                # 提交所有任务并使用进度条
                desc = f"  处理{category}流(批次{batch_start//batch_size + 1})" if len(all_flow_files) > batch_size else f"  处理{category}流"
                with tqdm(total=len(flow_args), desc=desc, unit="流") as pbar:
                    # 提交任务
                    future_to_flow = {
                        executor.submit(process_single_flow_with_label, args): args[0]
                        for args in flow_args
                    }

                    # 收集结果
                    batch_samples = []
                    error_count = 0
                    error_details = {}

                    for future in as_completed(future_to_flow):
                        try:
                            result = future.result(timeout=60)
                            if result:
                                if isinstance(result, tuple) and len(result) == 2 and result[0] == "error":
                                    # 错误结果
                                    error_type = result[1].split(':')[0] if ':' in result[1] else "未知错误"
                                    error_details[error_type] = error_details.get(error_type, 0) + 1
                                    error_count += 1
                                else:
                                    # 有效样本
                                    batch_samples.append(result)
                        except Exception as e:
                            flow_file = future_to_flow[future]
                            tqdm.write(f"    错误：流处理失败 {os.path.basename(flow_file)}: {e}")
                            error_count += 1

                        pbar.update(1)
                        pbar.set_postfix({
                            "批次样本": len(batch_samples),
                            "总样本": len(all_samples) + len(batch_samples),
                            "错误": error_count
                        })

                    # 显示错误统计
                    if error_details:
                        print(f"    批次错误统计:")
                        for error_type, count in error_details.items():
                            print(f"      {error_type}: {count} 个流文件")

                    all_samples.extend(batch_samples)

                    # 强制垃圾回收
                    gc.collect()

    except Exception as e:
        print(f"  错误：并行处理异常: {e}")

    finally:
        # 清理所有临时目录
        temp_dirs = set()
        for flow_file in all_flow_files:
            temp_dir = os.path.dirname(flow_file)
            if "minority_flows_" in temp_dir:
                temp_dirs.add(temp_dir)

        for temp_dir in temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception as e:
                print(f"    警告：清理临时目录失败 {temp_dir}: {e}")

    print(f"  类别 {category} 完成: {len(all_samples)} 个样本")
    return all_samples

def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        memory = psutil.virtual_memory()
        return {
            'used_percent': memory.percent,
            'available_gb': memory.available / (1024**3),
            'used_gb': memory.used / (1024**3)
        }
    except:
        return {'used_percent': 0, 'available_gb': 0, 'used_gb': 0}

def balance_samples_by_label(samples, target_samples_per_label=3000):
    """平衡样本数量，对每个标签限制最大样本数"""
    from collections import defaultdict
    import random

    # 按标签分组
    samples_by_label = defaultdict(list)
    for sample in samples:
        dataset_type, label_idx, payload_data = sample
        samples_by_label[label_idx].append(sample)

    balanced_samples = []

    for label_idx, label_samples in samples_by_label.items():
        if len(label_samples) > target_samples_per_label:
            # 下采样：随机选择目标数量的样本
            selected_samples = random.sample(label_samples, target_samples_per_label)
            print(f"    标签{label_idx}: {len(label_samples)} → {len(selected_samples)} 样本 (下采样)")
        else:
            selected_samples = label_samples
            print(f"    标签{label_idx}: {len(selected_samples)} 样本 (保持)")

        balanced_samples.extend(selected_samples)

    return balanced_samples

def create_clean_enhanced_dataset(new_samples, source_path, output_path):
    """创建干净的增强数据集：排除类别7和8的旧特征，只保留其他类别+新的增强特征"""

    print(f"开始创建干净的增强数据集...")
    print(f"源TSV路径: {source_path}")
    print(f"输出TSV路径: {output_path}")
    print(f"策略: 排除类别7和8的旧特征，保留其他类别特征 + 新增强特征")

    # 确保输出目录存在
    os.makedirs(output_path, exist_ok=True)

    # 按数据集类型分组新样本
    new_samples_by_type = {'train': [], 'dev': [], 'test': []}
    for dataset_type, label_idx, payload_data in new_samples:
        new_samples_by_type[dataset_type].append((label_idx, payload_data))

    total_merged = 0
    excluded_counts = {'train': 0, 'dev': 0, 'test': 0}

    for dataset_type in ['train', 'dev', 'test']:
        source_file = os.path.join(source_path, f"{dataset_type}_dataset.tsv")
        output_file = os.path.join(output_path, f"{dataset_type}_dataset_enhanced.tsv")

        other_category_samples = []  # 其他类别的样本
        excluded_old_samples = 0     # 被排除的类别7和8旧样本数
        new_samples_count = len(new_samples_by_type[dataset_type])

        # 读取现有TSV文件，只保留非类别7和8的样本
        if os.path.exists(source_file):
            with open(source_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter='\t')
                next(reader, None)  # 跳过头部
                for row in reader:
                    if len(row) >= 2:
                        label_idx = int(row[0])
                        if label_idx not in [7, 8]:  # 只保留非类别7和8的样本
                            other_category_samples.append((label_idx, row[1]))
                        else:
                            excluded_old_samples += 1

            print(f"  {dataset_type}: 保留其他类别样本 {len(other_category_samples)} 个")
            print(f"  {dataset_type}: 排除类别7和8旧样本 {excluded_old_samples} 个")
            excluded_counts[dataset_type] = excluded_old_samples
        else:
            print(f"  {dataset_type}: 源文件不存在，只写入新样本")

        # 写入干净的合并文件
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, delimiter='\t')
            writer.writerow(["label", "text_a"])  # 写入头部

            # 写入其他类别的样本
            for label_idx, payload_data in other_category_samples:
                writer.writerow([label_idx, payload_data])

            # 写入新的类别7和8增强特征样本
            for label_idx, payload_data in new_samples_by_type[dataset_type]:
                writer.writerow([label_idx, payload_data])

        total_samples = len(other_category_samples) + new_samples_count
        total_merged += total_samples

        print(f"  {dataset_type}: 合并完成 - 其他类别:{len(other_category_samples)} + 新增强:{new_samples_count} = 总计:{total_samples}")

        # 显示文件大小
        file_size = os.path.getsize(output_file) / (1024 * 1024)
        print(f"    {dataset_type}_dataset_enhanced.tsv: {file_size:.1f}MB")

    print(f"\n数据集清理统计:")
    print(f"  总计排除类别7和8旧样本: {sum(excluded_counts.values())} 个")
    print(f"  总计新增类别7和8增强样本: {len(new_samples)} 个")
    print(f"  最终数据集总样本数: {total_merged}")

    return total_merged

def append_samples_to_tsv(samples, output_path, balance_data=True):
    """将新样本追加到现有TSV文件中"""

    # 可选的数据平衡
    if balance_data and len(samples) > 1000:
        print(f"    数据平衡前: {len(samples)} 样本")
        samples = balance_samples_by_label(samples, target_samples_per_label=2000)
        print(f"    数据平衡后: {len(samples)} 样本")

    # 按数据集类型分组
    samples_by_type = {'train': [], 'dev': [], 'test': []}
    for dataset_type, label_idx, payload_data in samples:
        samples_by_type[dataset_type].append((label_idx, payload_data))

    # 追加到每个TSV文件
    total_written = 0
    for dataset_type, dataset_samples in samples_by_type.items():
        if not dataset_samples:
            continue

        tsv_file_path = os.path.join(output_path, f"{dataset_type}_dataset.tsv")

        # 检查文件是否存在，如果不存在则创建并写入头部
        file_exists = os.path.exists(tsv_file_path)

        # 以追加模式打开文件
        with open(tsv_file_path, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, delimiter='\t')

            # 如果文件不存在，写入头部
            if not file_exists:
                writer.writerow(["label", "text_a"])

            # 写入样本
            for label_idx, payload_data in dataset_samples:
                writer.writerow([label_idx, payload_data])

        total_written += len(dataset_samples)
        print(f"    {dataset_type}: +{len(dataset_samples)} 样本")

    print(f"    总计写入: {total_written} 个样本")

    # 显示当前TSV文件大小
    print(f"    当前TSV文件大小:")
    for dataset_name in ['train', 'dev', 'test']:
        tsv_file_path = os.path.join(output_path, f"{dataset_name}_dataset.tsv")
        if os.path.exists(tsv_file_path):
            file_size = os.path.getsize(tsv_file_path) / (1024 * 1024)
            print(f"      {dataset_name}_dataset.tsv: {file_size:.1f}MB")

def main():
    """主函数"""
    base_path = "D:/VPN通道识别/VPN通道数据集/"
    source_tsv_path = "D:/VPN通道识别/ET-BERT-main/results/vpn_tunnels_balanced/"
    output_path = "D:/VPN通道识别/ET-BERT-main/results/vpn_tunnels_class7_8_enhanced/"

    if not os.path.exists(base_path):
        print(f"错误：数据集路径不存在: {base_path}")
        return

    if not os.path.exists(source_tsv_path):
        print(f"错误：源TSV路径不存在: {source_tsv_path}")
        return

    # 设置随机种子
    random.seed(42)

    print("=" * 80)
    print("类别7和8增强特征提取脚本")
    print("=" * 80)
    print(f"处理类别: {list(MINORITY_VPN_CATEGORIES.keys())}")
    print(f"特征增强: payload + 包长度 + 香农熵")
    if FILES_PER_CATEGORY is None:
        print(f"处理模式: 全部文件")
    else:
        print(f"每类处理文件数: {FILES_PER_CATEGORY}")
    print(f"源TSV目录: {source_tsv_path}")
    print(f"输出目录: {output_path}")
    print("=" * 80)

    all_new_samples = []

    # 处理每个类别
    for i, (category, label_idx) in enumerate(MINORITY_VPN_CATEGORIES.items()):
        print(f"\n[{i+1}/{len(MINORITY_VPN_CATEGORIES)}] 开始处理类别: {category} (标签{label_idx})")

        # 显示内存使用情况
        memory_info = get_memory_usage()
        print(f"  当前内存使用: {memory_info['used_percent']:.1f}% ({memory_info['used_gb']:.1f}GB/{memory_info['used_gb']+memory_info['available_gb']:.1f}GB)")

        samples = process_single_category(category, label_idx, base_path)

        if samples:
            print(f"  类别 {category} 生成 {len(samples)} 个增强特征样本")
            all_new_samples.extend(samples)
            print(f"  累计新增样本: {len(all_new_samples)}")
        else:
            print(f"  类别 {category} 没有生成有效样本")

        # 强制垃圾回收
        gc.collect()

    if all_new_samples:
        print(f"\n总计生成 {len(all_new_samples)} 个增强特征样本")

        # 创建干净的增强数据集
        print(f"\n开始创建干净的增强数据集...")
        total_merged = create_clean_enhanced_dataset(all_new_samples, source_tsv_path, output_path)

        print("\n" + "=" * 80)
        print("类别7和8增强特征提取完成！")
        print("=" * 80)
        print("生成的增强TSV文件:")
        for dataset_name in ['train', 'dev', 'test']:
            tsv_file_path = os.path.join(output_path, f"{dataset_name}_dataset_enhanced.tsv")
            if os.path.exists(tsv_file_path):
                file_size = os.path.getsize(tsv_file_path) / (1024 * 1024)

                # 统计样本数
                with open(tsv_file_path, 'r', encoding='utf-8') as f:
                    sample_count = sum(1 for line in f) - 1  # 减去头部行

                print(f"- {dataset_name}_dataset_enhanced.tsv ({file_size:.1f}MB, {sample_count} 样本)")

        print(f"\n特征格式说明:")
        print(f"- 原始特征: bigram tokens")
        print(f"- 类别7和8增强特征: bigram tokens + [LENGTHS:包长度] + [ENTROPIES:香农熵]")
        print(f"- 可以使用增强TSV文件进行训练以提高类别7和8的分类准确率")

    else:
        print("\n没有生成新样本")

if __name__ == "__main__":
    # Windows多进程需要这个保护
    multiprocessing.freeze_support()
    main()
