#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用训练好的ET-BERT模型对TSV文件中的流量数据进行预测
参照 evaluate_model.py 和 run_classifier.py 的模型加载和预测逻辑
"""

import os
import sys
import json
import csv
import argparse
import torch
import pandas as pd
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fine-tuning'))

# 导入必要的模块
try:
    from uer.utils.vocab import Vocab
    from uer.utils.tokenizers import BertTokenizer
    from uer.model_builder import build_model
    from uer.utils.config import load_hyperparam
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在ET-BERT-main目录下运行此脚本，并且已安装UER库")
    sys.exit(1)

# 常量定义
CLS_TOKEN = "[CLS]"
DEFAULT_MODEL_PATH = "models/vpn_classifier_14class.bin"
DEFAULT_LABEL_MAPPING_PATH = "results/vpn_tunnels_class14/label_mapping_14class.json"
DEFAULT_CONFIG_PATH = "models/base_config.json"
DEFAULT_VOCAB_PATH = "models/encryptd_vocab.txt"


def process_line(line, columns, tokenizer, seq_length, label_to_index=None):
    """
    处理单行数据，将文本转换为模型输入格式
    参照 run_classifier.py 中的 process_line 函数
    """
    line = line.strip().split("\t")
    original_label = int(line[columns["label"]])

    # 如果有标签映射，使用映射后的索引；否则使用原始标签
    if label_to_index is not None:
        if original_label in label_to_index:
            label = label_to_index[original_label]
        else:
            raise ValueError(f"Label {original_label} not found in label mapping")
    else:
        label = original_label

    text_a = line[columns["text_a"]]
    src = tokenizer.convert_tokens_to_ids([CLS_TOKEN] + tokenizer.tokenize(text_a))
    seg = [1] * len(src)

    if len(src) > seq_length:
        src = src[:seq_length]
        seg = seg[:seq_length]
    while len(src) < seq_length:
        src.append(0)
        seg.append(0)

    return (src, label, seg)


def load_label_mapping(mapping_file_path):
    """加载标签映射文件"""
    try:
        with open(mapping_file_path, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        return mapping_data["index_to_label"], mapping_data["label_to_index"]
    except Exception as e:
        raise Exception(f"加载标签映射失败: {e}")


def load_model_and_tokenizer(model_path, config_path=None, vocab_path=None):
    """加载模型和分词器"""
    try:
        # 设置默认路径
        if config_path is None:
            config_path = DEFAULT_CONFIG_PATH
        if vocab_path is None:
            vocab_path = DEFAULT_VOCAB_PATH
            
        # 检查文件是否存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        if not os.path.exists(vocab_path):
            raise FileNotFoundError(f"词汇表文件不存在: {vocab_path}")
        
        # 加载配置
        args = load_hyperparam(config_path)
        args.labels_num = 14  # 14分类
        args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载词汇表和分词器
        vocab = Vocab()
        vocab.load(vocab_path)
        args.vocab_size = len(vocab)
        tokenizer = BertTokenizer(args, vocab)
        
        # 构建模型
        model = build_model(args)
        
        # 加载模型权重
        state_dict = torch.load(model_path, map_location=args.device)
        model.load_state_dict(state_dict)
        model.to(args.device)
        model.eval()
        
        return model, tokenizer, args
        
    except Exception as e:
        raise Exception(f"加载模型失败: {e}")


def preprocess_tsv_data(tsv_file_path, tokenizer, seq_length=512):
    """预处理TSV数据"""
    try:
        # 读取TSV文件
        df = pd.read_csv(tsv_file_path, sep='\t')
        
        # 检查必需的列
        if 'text_a' not in df.columns:
            raise ValueError("TSV文件必须包含 'text_a' 列")
        
        # 检查是否有标签列
        has_labels = 'label' in df.columns
        
        processed_data = []
        columns = {"text_a": df.columns.get_loc("text_a")}
        if has_labels:
            columns["label"] = df.columns.get_loc("label")
        
        for idx, row in df.iterrows():
            try:
                # 构造行数据
                if has_labels:
                    line_data = f"{row['label']}\t{row['text_a']}"
                else:
                    line_data = f"0\t{row['text_a']}"  # 使用占位符标签
                
                # 处理行数据
                if has_labels:
                    src, label, seg = process_line(line_data, columns, tokenizer, seq_length)
                else:
                    src, _, seg = process_line(line_data, columns, tokenizer, seq_length)
                    label = None
                
                processed_data.append({
                    'src': src,
                    'label': label,
                    'seg': seg,
                    'original_index': idx,
                    'original_text': row['text_a'][:100] + "..." if len(row['text_a']) > 100 else row['text_a']
                })
                
            except Exception as e:
                print(f"警告: 处理第{idx+1}行数据时出错: {e}")
                continue
        
        return processed_data, has_labels
        
    except Exception as e:
        raise Exception(f"预处理TSV数据失败: {e}")


def predict_batch(model, processed_data, args, index_to_label):
    """批量预测"""
    try:
        predictions = []
        
        with torch.no_grad():
            for data in processed_data:
                # 准备输入数据
                src = torch.LongTensor([data['src']]).to(args.device)
                seg = torch.LongTensor([data['seg']]).to(args.device)
                
                # 模型预测
                _, logits = model(src, seg)
                
                # 获取预测结果
                pred_probs = torch.softmax(logits, dim=-1)
                pred_label = torch.argmax(logits, dim=-1).item()
                confidence = pred_probs[0][pred_label].item()
                
                # 获取标签名称
                pred_label_name = index_to_label.get(str(pred_label), f"Unknown_{pred_label}")
                
                # 构造预测结果
                prediction = {
                    'index': data['original_index'],
                    'text_preview': data['original_text'],
                    'predicted_label': pred_label,
                    'predicted_label_name': pred_label_name,
                    'confidence': confidence,
                    'all_probabilities': pred_probs[0].cpu().numpy().tolist()
                }
                
                # 如果有真实标签，添加到结果中
                if data['label'] is not None:
                    true_label_name = index_to_label.get(str(data['label']), f"Unknown_{data['label']}")
                    prediction['true_label'] = data['label']
                    prediction['true_label_name'] = true_label_name
                    prediction['correct'] = (pred_label == data['label'])
                
                predictions.append(prediction)
        
        return predictions
        
    except Exception as e:
        raise Exception(f"批量预测失败: {e}")


def predict_tsv_with_model(tsv_file_path, model_path, output_file_path=None, debug=False):
    """
    使用训练好的ET-BERT模型对TSV文件中的流量数据进行预测
    
    Args:
        tsv_file_path (str): 输入TSV文件路径（包含label和text_a列）
        model_path (str): 训练好的模型文件路径（.bin文件）
        output_file_path (str, optional): 预测结果输出文件路径
        debug (bool, optional): 是否启用调试输出
        
    Returns:
        dict: 包含预测结果的字典，格式为 {'success': bool, 'predictions': list, 'message': str}
    """
    try:
        # 输入验证
        if not isinstance(tsv_file_path, str) or not tsv_file_path.strip():
            return {'success': False, 'predictions': [], 'message': '错误：TSV文件路径不能为空'}
        
        if not isinstance(model_path, str) or not model_path.strip():
            return {'success': False, 'predictions': [], 'message': '错误：模型文件路径不能为空'}
        
        if not os.path.exists(tsv_file_path):
            return {'success': False, 'predictions': [], 'message': f'错误：TSV文件不存在: {tsv_file_path}'}
        
        if not os.path.exists(model_path):
            return {'success': False, 'predictions': [], 'message': f'错误：模型文件不存在: {model_path}'}
        
        if debug:
            print(f"开始预测...")
            print(f"  TSV文件: {tsv_file_path}")
            print(f"  模型文件: {model_path}")
        
        # 加载标签映射
        if debug:
            print("  加载标签映射...")
        index_to_label, _ = load_label_mapping(DEFAULT_LABEL_MAPPING_PATH)
        
        # 加载模型和分词器
        if debug:
            print("  加载模型和分词器...")
        model, tokenizer, args = load_model_and_tokenizer(model_path)
        
        # 预处理数据
        if debug:
            print("  预处理TSV数据...")
        processed_data, has_labels = preprocess_tsv_data(tsv_file_path, tokenizer)
        
        if not processed_data:
            return {'success': False, 'predictions': [], 'message': '错误：没有有效的数据可以预测'}
        
        if debug:
            print(f"  成功预处理 {len(processed_data)} 个样本")
        
        # 批量预测
        if debug:
            print("  执行批量预测...")
        predictions = predict_batch(model, processed_data, args, index_to_label)
        
        # 计算统计信息
        total_samples = len(predictions)
        if has_labels:
            correct_predictions = sum(1 for p in predictions if p.get('correct', False))
            accuracy = correct_predictions / total_samples if total_samples > 0 else 0
        else:
            accuracy = None
        
        # 保存结果到文件（如果指定了输出路径）
        if output_file_path:
            save_predictions_to_file(predictions, output_file_path, has_labels, accuracy)
        
        # 构造返回结果
        result = {
            'success': True,
            'predictions': predictions,
            'message': f'成功预测 {total_samples} 个样本',
            'total_samples': total_samples,
            'has_labels': has_labels
        }
        
        if has_labels:
            result['accuracy'] = accuracy
            result['correct_predictions'] = correct_predictions
        
        if debug:
            print(f"  ✓ 预测完成: {result['message']}")
            if has_labels:
                print(f"  准确率: {accuracy:.4f} ({correct_predictions}/{total_samples})")
        
        return result
        
    except Exception as e:
        error_msg = f"预测过程中发生错误: {str(e)}"
        if debug:
            print(f"  ✗ {error_msg}")
        return {'success': False, 'predictions': [], 'message': error_msg}


def save_predictions_to_file(predictions, output_file_path, has_labels, accuracy=None):
    """保存预测结果到文件"""
    try:
        # 根据文件扩展名选择保存格式
        if output_file_path.lower().endswith('.json'):
            # 保存为JSON格式
            result_data = {
                'timestamp': datetime.now().isoformat(),
                'total_samples': len(predictions),
                'has_labels': has_labels,
                'predictions': predictions
            }
            if accuracy is not None:
                result_data['accuracy'] = accuracy
            
            with open(output_file_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
        else:
            # 保存为TSV格式
            with open(output_file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter='\t')
                
                # 写入头部
                headers = ['index', 'text_preview', 'predicted_label', 'predicted_label_name', 'confidence']
                if has_labels:
                    headers.extend(['true_label', 'true_label_name', 'correct'])
                writer.writerow(headers)
                
                # 写入数据
                for pred in predictions:
                    row = [
                        pred['index'],
                        pred['text_preview'],
                        pred['predicted_label'],
                        pred['predicted_label_name'],
                        f"{pred['confidence']:.4f}"
                    ]
                    if has_labels:
                        row.extend([
                            pred.get('true_label', ''),
                            pred.get('true_label_name', ''),
                            pred.get('correct', '')
                        ])
                    writer.writerow(row)
                
                # 如果有准确率信息，添加到文件末尾
                if accuracy is not None:
                    writer.writerow([])
                    writer.writerow(['# 统计信息'])
                    writer.writerow([f'# 总样本数: {len(predictions)}'])
                    writer.writerow([f'# 准确率: {accuracy:.4f}'])
        
    except Exception as e:
        raise Exception(f"保存预测结果失败: {e}")


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='使用ET-BERT模型对TSV文件进行VPN隧道类型预测')
    parser.add_argument('tsv_file', help='输入TSV文件路径（包含text_a列，可选label列）')
    parser.add_argument('--model', default=DEFAULT_MODEL_PATH, help='模型文件路径（.bin文件）')
    parser.add_argument('--output', '-o', help='预测结果输出文件路径（支持.json和.tsv格式）')
    parser.add_argument('--debug', action='store_true', help='启用调试输出')
    parser.add_argument('--show-top', type=int, default=5, help='显示前N个预测结果（默认5个）')

    args = parser.parse_args()

    print("=" * 70)
    print("ET-BERT VPN隧道类型预测工具")
    print("=" * 70)
    print(f"输入文件: {args.tsv_file}")
    print(f"模型文件: {args.model}")
    print(f"输出文件: {args.output if args.output else '不保存到文件'}")
    print(f"调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 70)

    # 执行预测
    result = predict_tsv_with_model(
        tsv_file_path=args.tsv_file,
        model_path=args.model,
        output_file_path=args.output,
        debug=args.debug
    )

    # 处理结果
    if result['success']:
        print(f"\n✓ {result['message']}")

        # 显示统计信息
        if result['has_labels']:
            print(f"  准确率: {result['accuracy']:.4f} ({result['correct_predictions']}/{result['total_samples']})")

        # 显示前几个预测结果
        predictions = result['predictions']
        if predictions:
            print(f"\n前 {min(args.show_top, len(predictions))} 个预测结果:")
            print("-" * 70)

            for i, pred in enumerate(predictions[:args.show_top]):
                print(f"样本 {pred['index']+1}:")
                print(f"  文本预览: {pred['text_preview']}")
                print(f"  预测标签: {pred['predicted_label']} ({pred['predicted_label_name']})")
                print(f"  置信度: {pred['confidence']:.4f}")

                if result['has_labels']:
                    print(f"  真实标签: {pred['true_label']} ({pred['true_label_name']})")
                    print(f"  预测正确: {'✓' if pred['correct'] else '✗'}")

                if i < min(args.show_top, len(predictions)) - 1:
                    print("-" * 40)

        # 显示标签分布
        if predictions:
            print(f"\n预测标签分布:")
            label_counts = {}
            for pred in predictions:
                label_name = pred['predicted_label_name']
                label_counts[label_name] = label_counts.get(label_name, 0) + 1

            for label_name, count in sorted(label_counts.items()):
                percentage = count / len(predictions) * 100
                print(f"  {label_name}: {count} 个样本 ({percentage:.1f}%)")

        if args.output:
            print(f"\n✓ 预测结果已保存到: {args.output}")

        return 0
    else:
        print(f"\n✗ {result['message']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())


# 使用示例（注释）
"""
使用方法:

1. 基本预测（仅包含特征数据）:
   python predict_with_model.py input.tsv

2. 预测并评估（包含真实标签）:
   python predict_with_model.py input.tsv --debug

3. 指定模型和输出文件:
   python predict_with_model.py input.tsv --model models/my_model.bin --output results.json

支持的输入格式:
- TSV文件必须包含 'text_a' 列（特征数据）
- 可选包含 'label' 列（真实标签，用于评估）

支持的输出格式:
- JSON格式: 包含完整的预测信息和元数据
- TSV格式: 表格形式，便于查看和分析
"""




"""
完整流程图
用户输入 (TSV文件 + 模型路径)
           ↓
    [main() 函数]
    - 解析命令行参数
    - 调用核心预测函数
           ↓
[predict_tsv_with_model()]
    ├── 1. 输入验证
    ├── 2. load_label_mapping()     ← 加载标签映射
    ├── 3. load_model_and_tokenizer() ← 加载模型和分词器
    ├── 4. preprocess_tsv_data()    ← 预处理TSV数据
    │   └── 调用 process_line() 处理每行
    ├── 5. predict_batch()          ← 批量预测
    │   ├── 转换为张量
    │   ├── 模型推理
    │   └── 格式化结果
    ├── 6. save_predictions_to_file() ← 保存结果(可选)
    └── 7. 返回结构化结果
           ↓
    [main() 函数]
    - 显示预测结果
    - 显示统计信息
    - 显示标签分布
"""