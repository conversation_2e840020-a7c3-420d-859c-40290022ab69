import os
import glob
import shutil
import random
from pathlib import Path
from tqdm import tqdm
import logging

def split_dataset_vpn(base_dir, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, copy_files=True):
    """
    将VPN通道数据集的MFR图像划分为训练集、验证集和测试集

    Args:
        base_dir: 包含各类别MFR图像的基础目录
        train_ratio: 训练集比例 (默认70%)
        val_ratio: 验证集比例 (默认15%)
        test_ratio: 测试集比例 (默认15%)
        copy_files: 是否复制文件而非移动 (默认True，保留原文件)

    输入目录结构:
    base_dir/
    ├── OpenVPN_Vless/
    │   ├── img1.png
    │   └── img2.png
    └── Vmess/
        ├── img3.png
        └── img4.png

    输出目录结构:
    base_dir/
    ├── train/
    │   ├── OpenVPN_Vless/
    │   └── Vmess/
    ├── val/
    │   ├── OpenVPN_Vless/
    │   └── Vmess/
    └── test/
        ├── OpenVPN_Vless/
        └── Vmess/
    """
    # 验证比例总和
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 0.001:
        raise ValueError(f"比例总和必须为1.0，当前为: {train_ratio + val_ratio + test_ratio}")

    base_path = Path(base_dir)

    # 查找类别目录（排除已存在的划分目录）
    source_dirs = [d for d in base_path.iterdir()
                   if d.is_dir() and d.name not in ['train', 'val', 'test']]

    if not source_dirs:
        print(f"错误: 在 '{base_dir}' 中未找到类别目录")
        print("脚本期望在基础目录中找到各类别的子目录")
        return False

    # 创建输出目录
    train_path = base_path / 'train'
    val_path = base_path / 'val'
    test_path = base_path / 'test'

    print(f"创建训练集目录: {train_path}")
    train_path.mkdir(exist_ok=True)
    print(f"创建验证集目录: {val_path}")
    val_path.mkdir(exist_ok=True)
    print(f"创建测试集目录: {test_path}")
    test_path.mkdir(exist_ok=True)

    # 统计信息
    total_files = 0
    class_stats = {}

    print(f"\n发现 {len(source_dirs)} 个类别目录:")
    for class_dir in source_dirs:
        files = list(class_dir.glob('*.png'))
        class_stats[class_dir.name] = len(files)
        total_files += len(files)
        print(f"  {class_dir.name}: {len(files)} 个文件")

    print(f"\n总文件数: {total_files}")
    print(f"划分比例: 训练集 {train_ratio*100:.1f}%, 验证集 {val_ratio*100:.1f}%, 测试集 {test_ratio*100:.1f}%")

    # 处理各类别目录
    print("\n开始处理类别目录...")
    split_stats = {'train': 0, 'val': 0, 'test': 0}

    for class_dir in tqdm(source_dirs, desc="处理类别"):
        class_name = class_dir.name

        # 创建各子集的类别目录
        train_class_path = train_path / class_name
        train_class_path.mkdir(exist_ok=True)

        val_class_path = val_path / class_name
        val_class_path.mkdir(exist_ok=True)

        test_class_path = test_path / class_name
        test_class_path.mkdir(exist_ok=True)

        # 获取所有PNG文件并随机打乱
        files = list(class_dir.glob('*.png'))
        if not files:
            print(f"警告: 类别 {class_name} 中没有PNG文件")
            continue

        random.shuffle(files)

        # 计算划分索引
        train_split = int(len(files) * train_ratio)
        val_split = int(len(files) * (train_ratio + val_ratio))

        train_files = files[:train_split]
        val_files = files[train_split:val_split]
        test_files = files[val_split:]

        # 更新统计
        split_stats['train'] += len(train_files)
        split_stats['val'] += len(val_files)
        split_stats['test'] += len(test_files)

        # 复制或移动文件
        operation = shutil.copy2 if copy_files else shutil.move
        operation_name = "复制" if copy_files else "移动"

        # 处理训练集文件
        for file_path in tqdm(train_files, desc=f'{operation_name} {class_name} (训练集)', leave=False):
            operation(str(file_path), str(train_class_path / file_path.name))

        # 处理验证集文件
        for file_path in tqdm(val_files, desc=f'{operation_name} {class_name} (验证集)', leave=False):
            operation(str(file_path), str(val_class_path / file_path.name))

        # 处理测试集文件
        for file_path in tqdm(test_files, desc=f'{operation_name} {class_name} (测试集)', leave=False):
            operation(str(file_path), str(test_class_path / file_path.name))

    # 输出最终统计
    print(f"\n数据集划分完成!")
    print(f"训练集: {split_stats['train']} 个文件 ({split_stats['train']/total_files*100:.1f}%)")
    print(f"验证集: {split_stats['val']} 个文件 ({split_stats['val']/total_files*100:.1f}%)")
    print(f"测试集: {split_stats['test']} 个文件 ({split_stats['test']/total_files*100:.1f}%)")

    # 如果是移动文件，清理空的源目录
    if not copy_files:
        print("\n清理空的源目录...")
        for class_dir in source_dirs:
            try:
                class_dir.rmdir()
                print(f"已删除空目录: {class_dir}")
            except OSError:
                print(f"目录非空，保留: {class_dir}")

    return True


def validate_split_results(base_dir):
    """验证数据集划分结果"""
    base_path = Path(base_dir)

    train_path = base_path / 'train'
    val_path = base_path / 'val'
    test_path = base_path / 'test'

    if not all([train_path.exists(), val_path.exists(), test_path.exists()]):
        print("错误: 划分目录不完整")
        return False

    print("\n=== 数据集划分验证 ===")

    # 获取所有类别
    train_classes = set(d.name for d in train_path.iterdir() if d.is_dir())
    val_classes = set(d.name for d in val_path.iterdir() if d.is_dir())
    test_classes = set(d.name for d in test_path.iterdir() if d.is_dir())

    all_classes = train_classes | val_classes | test_classes

    print(f"发现 {len(all_classes)} 个类别: {sorted(all_classes)}")

    # 检查类别一致性
    if train_classes == val_classes == test_classes:
        print("✓ 所有子集包含相同的类别")
    else:
        print("⚠ 警告: 子集间类别不一致")
        print(f"  训练集类别: {sorted(train_classes)}")
        print(f"  验证集类别: {sorted(val_classes)}")
        print(f"  测试集类别: {sorted(test_classes)}")

    # 统计各子集文件数量
    total_stats = {'train': 0, 'val': 0, 'test': 0}
    class_distribution = {}

    for class_name in sorted(all_classes):
        train_count = len(list((train_path / class_name).glob('*.png'))) if (train_path / class_name).exists() else 0
        val_count = len(list((val_path / class_name).glob('*.png'))) if (val_path / class_name).exists() else 0
        test_count = len(list((test_path / class_name).glob('*.png'))) if (test_path / class_name).exists() else 0

        total_count = train_count + val_count + test_count

        class_distribution[class_name] = {
            'train': train_count,
            'val': val_count,
            'test': test_count,
            'total': total_count
        }

        total_stats['train'] += train_count
        total_stats['val'] += val_count
        total_stats['test'] += test_count

        if total_count > 0:
            print(f"\n{class_name}:")
            print(f"  训练集: {train_count:4d} ({train_count/total_count*100:5.1f}%)")
            print(f"  验证集: {val_count:4d} ({val_count/total_count*100:5.1f}%)")
            print(f"  测试集: {test_count:4d} ({test_count/total_count*100:5.1f}%)")
            print(f"  总计:   {total_count:4d}")

    # 总体统计
    total_files = sum(total_stats.values())
    print(f"\n=== 总体统计 ===")
    print(f"训练集: {total_stats['train']:4d} 个文件 ({total_stats['train']/total_files*100:5.1f}%)")
    print(f"验证集: {total_stats['val']:4d} 个文件 ({total_stats['val']/total_files*100:5.1f}%)")
    print(f"测试集: {total_stats['test']:4d} 个文件 ({total_stats['test']/total_files*100:5.1f}%)")
    print(f"总计:   {total_files:4d} 个文件")

    return True

if __name__ == '__main__':
    # ==================== VPN数据集划分配置 ====================

    # VPN通道数据集MFR图像目录
    DATASET_BASE_DIR = 'D:/VPN通道识别/YaTC-main/data/VPN_MFR'

    # 数据集划分比例
    TRAIN_RATIO = 0.7   # 70% 训练集
    VAL_RATIO = 0.15    # 15% 验证集
    TEST_RATIO = 0.15   # 15% 测试集

    # 是否复制文件（True=保留原文件，False=移动文件）
    COPY_FILES = True

    # ==========================================================

    print("=" * 60)
    print("YaTC VPN通道数据集划分工具")
    print("=" * 60)
    print(f"源目录: {DATASET_BASE_DIR}")
    print(f"划分比例: 训练集 {TRAIN_RATIO*100:.0f}% | 验证集 {VAL_RATIO*100:.0f}% | 测试集 {TEST_RATIO*100:.0f}%")
    print(f"文件操作: {'复制' if COPY_FILES else '移动'}")
    print("=" * 60)

    # 检查源目录是否存在
    if not os.path.exists(DATASET_BASE_DIR):
        print(f"错误: 源目录不存在 - {DATASET_BASE_DIR}")
        print("请确保MFR图像生成已完成")
        exit(1)

    try:
        # 执行数据集划分
        success = split_dataset_vpn(
            DATASET_BASE_DIR,
            train_ratio=TRAIN_RATIO,
            val_ratio=VAL_RATIO,
            test_ratio=TEST_RATIO,
            copy_files=COPY_FILES
        )

        if success:
            # 验证划分结果
            validate_split_results(DATASET_BASE_DIR)
            print("\n" + "=" * 60)
            print("数据集划分完成!")
            print("=" * 60)
        else:
            print("数据集划分失败!")

    except Exception as e:
        print(f"划分过程中发生错误: {e}")
        import traceback
        traceback.print_exc()