import os
import glob
import shutil
import random
from pathlib import Path
from tqdm import tqdm

def split_dataset(base_dir, train_ratio=0.8):
    """
    Splits a dataset of images into training and testing sets.

    The expected directory structure is:
    base_dir/
    ├── class1/
    │   ├── img1.png
    │   └── img2.png
    └── class2/
        ├── img3.png
        └── img4.png

    The function will create:
    base_dir/
    ├── train/
    │   ├── class1/
    │   └── class2/
    └── test/
        ├── class1/
        └── class2/
    """
    base_path = Path(base_dir)
    source_dirs = [d for d in base_path.iterdir() if d.is_dir() and d.name not in ['train', 'test']]

    if not source_dirs:
        print(f"Error: No class directories found in '{base_dir}'.")
        print("The script expected to find subdirectories for each class directly inside the base directory.")
        return

    train_path = base_path / 'train'
    test_path = base_path / 'test'

    print(f"Creating 'train' directory at: {train_path}")
    train_path.mkdir(exist_ok=True)
    print(f"Creating 'test' directory at: {test_path}")
    test_path.mkdir(exist_ok=True)

    print("\nProcessing class directories...")
    for class_dir in tqdm(source_dirs, desc="Classes"):
        class_name = class_dir.name
        
        train_class_path = train_path / class_name
        train_class_path.mkdir(exist_ok=True)
        
        test_class_path = test_path / class_name
        test_class_path.mkdir(exist_ok=True)

        files = list(class_dir.glob('*.png'))
        random.shuffle(files)

        split_index = int(len(files) * train_ratio)
        train_files = files[:split_index]
        test_files = files[split_index:]

        for file_path in tqdm(train_files, desc=f'Moving {class_name} (train)', leave=False):
            shutil.move(str(file_path), str(train_class_path / file_path.name))
        
        for file_path in tqdm(test_files, desc=f'Moving {class_name} (test)', leave=False):
            shutil.move(str(file_path), str(test_class_path / file_path.name))
            
    print("\nDataset splitting complete.")
    # Optional: Clean up empty source directories
    for class_dir in source_dirs:
        try:
            class_dir.rmdir() # This will only remove if the directory is empty
            print(f"Removed empty source directory: {class_dir}")
        except OSError:
            # Directory not empty, which is unexpected but we can ignore.
            pass


if __name__ == '__main__':
    # --- Configuration ---
    # The directory where your class folders (e.g., '恶意文件', '良性') are located.
    DATASET_BASE_DIR = 'data'
    
    # The ratio of data to be used for training (e.g., 0.8 means 80% train, 20% test)
    TRAIN_SPLIT_RATIO = 0.8
    # ---------------------

    print("--- Dataset Splitter ---")
    print(f"Source Directory:  {DATASET_BASE_DIR}")
    print(f"Train/Test Split:  {TRAIN_SPLIT_RATIO*100:.0f}% / {(1-TRAIN_SPLIT_RATIO)*100:.0f}%")
    
    # A simple confirmation prompt
    # answer = input("Proceed? (y/n): ").lower()
    # if answer == 'y':
    split_dataset(DATASET_BASE_DIR, TRAIN_SPLIT_RATIO)
    # else:
    #     print("Operation cancelled.") 