"D:\VPN通道识别\ET-BERT-main\results\vpn_tunnels_class14"是产生的数据集
"D:\VPN通道识别\ET-BERT-main\evaluate_model.py"可以用测试集评估模型
"D:\VPN通道识别\ET-BERT-main\models\vpn_classifier_14class.bin"是微调好的模型
"D:\VPN通道识别\ET-BERT-main\vpn_14class_evaluation_report.txt"和"D:\VPN通道识别\ET-BERT-main\sample_predictions.txt"是评估报告
"D:\VPN通道识别\ET-BERT-main\train_vpn_classifier.py"是微调代码
"D:\VPN通道识别\ET-BERT-main\process_single_flow.py"是单个pcap流文件到tsv的过程
"D:\VPN通道识别\ET-BERT-main\predict_with_model.py"是tsv到评估结果的过程