#!/usr/bin/python3
# -*- coding:utf-8 -*-

"""
数据集平衡脚本
重新平衡TSV数据集，解决类别不平衡问题
"""

import pandas as pd
import numpy as np
import os
import random
from collections import Counter

def balance_dataset(input_path, output_path, strategy='undersample', target_samples=3000):
    """
    平衡数据集
    
    Args:
        input_path: 输入TSV文件路径
        output_path: 输出TSV文件路径  
        strategy: 平衡策略 ('undersample', 'oversample', 'mixed')
        target_samples: 每个类别的目标样本数
    """
    
    print(f"处理文件: {input_path}")
    
    # 读取数据
    df = pd.read_csv(input_path, sep='\t')
    print(f"原始数据: {len(df)} 样本")
    
    # 统计各标签分布
    label_counts = df['label'].value_counts().sort_index()
    print("原始标签分布:")
    for label, count in label_counts.items():
        print(f"  标签{label}: {count} 样本")
    
    # 按标签分组
    grouped = df.groupby('label')
    balanced_dfs = []
    
    for label, group in grouped:
        current_count = len(group)
        
        if strategy == 'undersample':
            # 下采样：限制最大样本数
            if current_count > target_samples:
                sampled = group.sample(n=target_samples, random_state=42)
                print(f"  标签{label}: {current_count} → {len(sampled)} (下采样)")
            else:
                sampled = group
                print(f"  标签{label}: {current_count} (保持)")
                
        elif strategy == 'oversample':
            # 上采样：重复样本达到目标数量
            if current_count < target_samples:
                # 重复采样
                n_repeats = target_samples // current_count
                remainder = target_samples % current_count
                
                repeated = pd.concat([group] * n_repeats, ignore_index=True)
                if remainder > 0:
                    extra = group.sample(n=remainder, random_state=42)
                    repeated = pd.concat([repeated, extra], ignore_index=True)
                
                sampled = repeated
                print(f"  标签{label}: {current_count} → {len(sampled)} (上采样)")
            else:
                sampled = group
                print(f"  标签{label}: {current_count} (保持)")
                
        elif strategy == 'mixed':
            # 混合策略：下采样大类，上采样小类
            if current_count > target_samples:
                # 下采样
                sampled = group.sample(n=target_samples, random_state=42)
                print(f"  标签{label}: {current_count} → {len(sampled)} (下采样)")
            elif current_count < target_samples // 2:
                # 上采样小类
                n_repeats = (target_samples // 2) // current_count
                remainder = (target_samples // 2) % current_count
                
                repeated = pd.concat([group] * n_repeats, ignore_index=True)
                if remainder > 0:
                    extra = group.sample(n=remainder, random_state=42)
                    repeated = pd.concat([repeated, extra], ignore_index=True)
                
                sampled = repeated
                print(f"  标签{label}: {current_count} → {len(sampled)} (上采样)")
            else:
                sampled = group
                print(f"  标签{label}: {current_count} (保持)")
        
        balanced_dfs.append(sampled)
    
    # 合并所有平衡后的数据
    balanced_df = pd.concat(balanced_dfs, ignore_index=True)
    
    # 随机打乱
    balanced_df = balanced_df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    print(f"平衡后数据: {len(balanced_df)} 样本")
    
    # 统计平衡后的分布
    new_label_counts = balanced_df['label'].value_counts().sort_index()
    print("平衡后标签分布:")
    for label, count in new_label_counts.items():
        print(f"  标签{label}: {count} 样本")
    
    # 保存
    balanced_df.to_csv(output_path, sep='\t', index=False)
    print(f"保存到: {output_path}")
    
    return balanced_df

def main():
    """主函数"""
    
    input_dir = "results/vpn_tunnels_batch"
    output_dir = "results/vpn_tunnels_balanced"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("=" * 60)
    print("数据集平衡脚本")
    print("=" * 60)
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    datasets = ['train', 'dev', 'test']
    
    for dataset in datasets:
        input_file = os.path.join(input_dir, f"{dataset}_dataset.tsv")
        output_file = os.path.join(output_dir, f"{dataset}_dataset.tsv")
        
        if os.path.exists(input_file):
            print(f"\n处理 {dataset} 数据集...")
            
            # 根据数据集选择不同的策略
            if dataset == 'train':
                # 训练集使用混合策略，目标每类2000样本
                balance_dataset(input_file, output_file, strategy='mixed', target_samples=2000)
            else:
                # 验证集和测试集使用混合策略，确保每类至少有足够样本
                balance_dataset(input_file, output_file, strategy='mixed', target_samples=300)
        else:
            print(f"文件不存在: {input_file}")
    
    print("\n" + "=" * 60)
    print("数据集平衡完成！")
    print("=" * 60)
    print("平衡后的文件保存在: results/vpn_tunnels_balanced/")
    print("- train_dataset.tsv")
    print("- dev_dataset.tsv")
    print("- test_dataset.tsv")

if __name__ == "__main__":
    main()
