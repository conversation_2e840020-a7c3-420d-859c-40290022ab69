#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN通道数据集处理性能测试脚本
用于测试和监控优化后的data_process.py性能
"""

import os
import sys
import time
import psutil
import matplotlib.pyplot as plt
from collections import defaultdict
import threading
import queue

# 添加YaTC-main目录到Python路径
sys.path.insert(0, 'YaTC-main')

try:
    from data_process import (
        setup_logging, 
        MFR_generator,
        monitor_memory_usage,
        read_5hp_list_optimized
    )
    print("✓ 成功导入优化后的data_process模块")
except ImportError as e:
    print(f"✗ 导入data_process模块失败: {e}")
    sys.exit(1)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.memory_data = []
        self.cpu_data = []
        self.time_data = []
        self.file_count_data = []
        self.monitoring = False
        self.start_time = None
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start_time = time.time()
        self.memory_data = []
        self.cpu_data = []
        self.time_data = []
        
        def monitor_loop():
            while self.monitoring:
                current_time = time.time() - self.start_time
                memory_mb = monitor_memory_usage()
                cpu_percent = psutil.cpu_percent(interval=1)
                
                self.time_data.append(current_time)
                self.memory_data.append(memory_mb)
                self.cpu_data.append(cpu_percent)
                
                time.sleep(5)  # 每5秒记录一次
        
        self.monitor_thread = threading.Thread(target=monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=2)
    
    def plot_performance(self, save_path="performance_report.png"):
        """绘制性能图表"""
        if not self.time_data:
            print("没有性能数据可绘制")
            return
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # 内存使用图
        ax1.plot(self.time_data, self.memory_data, 'b-', linewidth=2)
        ax1.set_xlabel('时间 (秒)')
        ax1.set_ylabel('内存使用 (MB)')
        ax1.set_title('内存使用趋势')
        ax1.grid(True, alpha=0.3)
        
        # CPU使用图
        ax2.plot(self.time_data, self.cpu_data, 'r-', linewidth=2)
        ax2.set_xlabel('时间 (秒)')
        ax2.set_ylabel('CPU使用率 (%)')
        ax2.set_title('CPU使用趋势')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"性能图表已保存到: {save_path}")
    
    def get_summary(self):
        """获取性能摘要"""
        if not self.memory_data:
            return "没有性能数据"
        
        max_memory = max(self.memory_data)
        min_memory = min(self.memory_data)
        avg_memory = sum(self.memory_data) / len(self.memory_data)
        
        max_cpu = max(self.cpu_data)
        avg_cpu = sum(self.cpu_data) / len(self.cpu_data)
        
        return f"""
性能摘要:
- 监控时长: {self.time_data[-1]:.1f} 秒
- 内存使用: 最小 {min_memory:.1f} MB, 最大 {max_memory:.1f} MB, 平均 {avg_memory:.1f} MB
- 内存增长: {max_memory - min_memory:.1f} MB
- CPU使用: 最大 {max_cpu:.1f}%, 平均 {avg_cpu:.1f}%
"""

def test_single_file_performance():
    """测试单个文件处理性能"""
    print("\n" + "="*50)
    print("单文件处理性能测试")
    print("="*50)
    
    # 查找测试文件
    test_files = []
    vpn_dataset_path = "VPN通道数据集"
    
    for class_dir in os.listdir(vpn_dataset_path):
        class_path = os.path.join(vpn_dataset_path, class_dir)
        if os.path.isdir(class_path):
            pcap_files = [os.path.join(class_path, f) for f in os.listdir(class_path) 
                         if f.endswith('.pcap')]
            test_files.extend(pcap_files[:5])  # 每个类别取5个文件
            if len(test_files) >= 20:  # 总共测试20个文件
                break
    
    if not test_files:
        print("未找到测试文件")
        return
    
    print(f"测试文件数量: {len(test_files)}")
    
    # 测试处理时间
    processing_times = []
    memory_usage = []
    
    for i, test_file in enumerate(test_files):
        start_memory = monitor_memory_usage()
        start_time = time.time()
        
        # 处理文件
        result = read_5hp_list_optimized(test_file)
        
        end_time = time.time()
        end_memory = monitor_memory_usage()
        
        processing_time = end_time - start_time
        memory_delta = end_memory - start_memory
        
        processing_times.append(processing_time)
        memory_usage.append(memory_delta)
        
        if i % 5 == 0:
            print(f"已测试 {i+1}/{len(test_files)} 个文件")
    
    # 分析结果
    avg_time = sum(processing_times) / len(processing_times)
    max_time = max(processing_times)
    min_time = min(processing_times)
    
    avg_memory = sum(memory_usage) / len(memory_usage)
    max_memory = max(memory_usage)
    
    print(f"\n单文件处理性能:")
    print(f"- 平均处理时间: {avg_time:.3f} 秒")
    print(f"- 最快处理时间: {min_time:.3f} 秒")
    print(f"- 最慢处理时间: {max_time:.3f} 秒")
    print(f"- 平均内存变化: {avg_memory:.1f} MB")
    print(f"- 最大内存变化: {max_memory:.1f} MB")
    
    # 检查性能稳定性
    time_variance = sum((t - avg_time) ** 2 for t in processing_times) / len(processing_times)
    time_std = time_variance ** 0.5
    
    if time_std / avg_time < 0.2:  # 标准差小于平均值的20%
        print("✓ 处理时间稳定")
    else:
        print("⚠ 处理时间波动较大")

def test_batch_processing_performance():
    """测试批量处理性能"""
    print("\n" + "="*50)
    print("批量处理性能测试")
    print("="*50)
    
    # 选择一个较小的类别进行测试
    test_class = "openvpn"
    output_path = "performance_test_output"
    
    print(f"测试类别: {test_class}")
    print(f"输出路径: {output_path}")
    
    # 启动性能监控
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    try:
        # 设置日志
        logger = setup_logging()
        
        # 运行批量处理
        start_time = time.time()
        MFR_generator("VPN通道数据集", output_path, specific_class=test_class)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # 停止监控
        monitor.stop_monitoring()
        
        # 统计结果
        class_path = os.path.join("VPN通道数据集", test_class)
        total_files = len([f for f in os.listdir(class_path) if f.endswith('.pcap')])
        
        output_class_path = os.path.join(output_path, "OpenVPN")  # 映射后的类别名
        if os.path.exists(output_class_path):
            processed_files = len([f for f in os.listdir(output_class_path) if f.endswith('.png')])
        else:
            processed_files = 0
        
        print(f"\n批量处理结果:")
        print(f"- 总处理时间: {processing_time:.2f} 秒")
        print(f"- 处理文件数: {processed_files}/{total_files}")
        print(f"- 平均速度: {processed_files/processing_time:.2f} 文件/秒")
        
        # 显示性能摘要
        print(monitor.get_summary())
        
        # 生成性能图表
        monitor.plot_performance("batch_performance.png")
        
        return processing_time, processed_files, total_files
        
    except Exception as e:
        monitor.stop_monitoring()
        print(f"批量处理测试失败: {e}")
        return None, 0, 0

def main():
    """主测试函数"""
    print("VPN通道数据集处理性能测试")
    print("="*60)
    
    # 检查依赖
    try:
        import matplotlib.pyplot as plt
        print("✓ matplotlib 可用")
    except ImportError:
        print("⚠ matplotlib 不可用，将跳过图表生成")
    
    # 运行性能测试
    test_single_file_performance()
    
    processing_time, processed_files, total_files = test_batch_processing_performance()
    
    # 输出总结
    print("\n" + "="*60)
    print("性能测试总结")
    print("="*60)
    
    if processing_time:
        success_rate = processed_files / total_files if total_files > 0 else 0
        print(f"批量处理成功率: {success_rate:.1%}")
        print(f"处理速度: {processed_files/processing_time:.2f} 文件/秒")
        
        if success_rate > 0.95:
            print("✓ 处理成功率良好")
        else:
            print("⚠ 处理成功率需要改进")
        
        if processed_files/processing_time > 1.0:
            print("✓ 处理速度良好")
        else:
            print("⚠ 处理速度需要优化")
    
    print("\n建议:")
    print("1. 如果内存使用持续增长，考虑减少批次大小")
    print("2. 如果CPU使用率过低，可以增加进程数")
    print("3. 如果处理速度慢，检查磁盘I/O性能")
    print("4. 定期监控长时间运行的处理任务")

if __name__ == "__main__":
    main()
