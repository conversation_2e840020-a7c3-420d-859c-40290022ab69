#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复混淆矩阵提取问题
"""

import json
import pandas as pd

# 从报告中手动提取的正确混淆矩阵
correct_confusion_matrix = [
    [200,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0],
    [ 15, 182,   0,   0,   0,   0,   3,   0,   0,   0,   0,   0,   0,   0],
    [  0,   0, 180,   0,   7,   0,  12,   0,   0,   0,   1,   0,   0,   0],
    [  0,   0,   0, 192,   0,   0,   0,   0,   0,   0,   6,   0,   2,   0],
    [  0,  20,  57,   0,  70,   0,  52,   0,   0,   0,   1,   0,   0,   0],
    [  0,   0,   0,   0,   0, 195,   1,   3,   0,   0,   0,   1,   0,   0],
    [  0,  64,   7,   0,   5,   0, 124,   0,   0,   0,   0,   0,   0,   0],
    [  0,   0,   0,   0,   0,   0,   0, 342,   0,   0,   0,   0,   0,   0],
    [  0,   0,   0,   0,   0,   1,   0,   0, 185,   4,   6,   4,   0,   0],
    [  0,   0,   0,   0,   0,   0,   0,   0,   0, 200,   0,   0,   0,   0],
    [  0,   0,   1,   0,   0,   1,   0,   0,   0,   0, 191,   7,   0,   0],
    [  0,   0,   0,   0,   0,   1,   1,   5,   4,   8,   7, 153,   0,  21],
    [  0,   0,   0,   0,   0,   0,   1,   3,   0,   0,   0,   0, 196,   0],
    [  0,   0,   1,   0,   0,   0,   0,   6,   0,   0,   2,   7,   0, 184]
]

# 加载标签映射
with open("results/vpn_tunnels_class14/label_mapping_14class.json", "r", encoding="utf-8") as f:
    mapping_data = json.load(f)
    index_to_label = mapping_data["index_to_label"]

# 加载测试数据
test_df = pd.read_csv("results/vpn_tunnels_class14/test_dataset_14class.tsv", sep='\t')

# 创建输出文件
output_file = "sample_predictions.txt"
output_lines = []

output_lines.append("=== VPN隧道14分类模型 - 详细样本预测结果 ===")
output_lines.append(f"生成时间: {pd.Timestamp.now()}")
output_lines.append("")

output_lines.append("=== 混淆矩阵分析 ===")
output_lines.append(f"混淆矩阵大小: {len(correct_confusion_matrix)}x{len(correct_confusion_matrix[0])}")

# 验证混淆矩阵
total_from_matrix = sum(sum(row) for row in correct_confusion_matrix)
correct_from_matrix = sum(correct_confusion_matrix[i][i] for i in range(len(correct_confusion_matrix)))
matrix_accuracy = correct_from_matrix / total_from_matrix if total_from_matrix > 0 else 0

output_lines.append(f"混淆矩阵总样本: {total_from_matrix}")
output_lines.append(f"混淆矩阵正确预测: {correct_from_matrix}")
output_lines.append(f"混淆矩阵准确率: {matrix_accuracy:.4f}")
output_lines.append(f"测试集总样本: {len(test_df)}")
output_lines.append(f"样本数匹配: {'✓' if total_from_matrix == len(test_df) else '✗'}")
output_lines.append("")

# 生成每个样本的预测结果
output_lines.append("=== 各类别预测序列生成 ===")

# 根据混淆矩阵重建预测序列
class_prediction_sequences = {}
class_indices = {}

for true_label in range(14):
    predictions = []
    for pred_label in range(14):
        count = correct_confusion_matrix[true_label][pred_label]
        predictions.extend([pred_label] * count)

    class_prediction_sequences[true_label] = predictions
    class_indices[true_label] = 0

    output_lines.append(f"类别{true_label} ({index_to_label[str(true_label)]}): {len(predictions)}个预测")

output_lines.append("")

# 生成每个样本的预测
output_lines.append("=== 详细样本预测结果 ===")
output_lines.append("   序号 | 真实标签        | 预测标签        | 结果")
output_lines.append("   -----|----------------|----------------|------")

total_processed = 0
correct_predictions = 0

for sample_idx, (_, row) in enumerate(test_df.iterrows()):
    true_label = row['label']
    true_name = index_to_label[str(true_label)]

    # 获取该类别的下一个预测
    pred_sequence = class_prediction_sequences[true_label]
    current_idx = class_indices[true_label]

    if current_idx < len(pred_sequence):
        pred_label = pred_sequence[current_idx]
        pred_name = index_to_label[str(pred_label)]
        correct_mark = "✓" if pred_label == true_label else "✗"

        if correct_mark == "✓":
            correct_predictions += 1

        class_indices[true_label] += 1
        total_processed += 1

        output_lines.append(f"   {sample_idx+1:4d} | {true_name:15s} | {pred_name:15s} | {correct_mark}")
    else:
        output_lines.append(f"   {sample_idx+1:4d} | {true_name:15s} | {'ERROR':15s} | ⚠")

output_lines.append("")
output_lines.append("=== 最终统计 ===")
output_lines.append(f"总样本数: {len(test_df)}")
output_lines.append(f"处理样本数: {total_processed}")
output_lines.append(f"正确预测: {correct_predictions}")
output_lines.append(f"错误预测: {total_processed - correct_predictions}")
output_lines.append(f"准确率: {correct_predictions / total_processed:.4f}")
output_lines.append(f"与混淆矩阵准确率匹配: {'✓' if abs(correct_predictions / total_processed - matrix_accuracy) < 0.001 else '✗'}")

# 写入文件
with open(output_file, 'w', encoding='utf-8') as f:
    for line in output_lines:
        f.write(line + '\n')

print(f"✓ 详细样本预测结果已保存到: {output_file}")
print(f"✓ 总样本数: {len(test_df)}")
print(f"✓ 处理样本数: {total_processed}")
print(f"✓ 准确率: {correct_predictions / total_processed:.4f}")
print(f"✓ 文件包含 {len(output_lines)} 行详细结果")
