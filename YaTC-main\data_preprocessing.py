#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN通道数据集预处理脚本
解决数据不平衡和数据增强问题，为YaTC模型训练做准备

功能：
1. 数据不平衡分析和处理
2. MFR图像数据增强
3. 生成平衡的数据集
4. 与YaTC fine-tune.py兼容
"""

import os
import json
import numpy as np
import pandas as pd
from PIL import Image, ImageEnhance, ImageFilter
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import shutil
from collections import Counter, defaultdict
import random
from tqdm import tqdm
import logging
from sklearn.utils.class_weight import compute_class_weight
import cv2

# 设置随机种子
random.seed(42)
np.random.seed(42)

class VPNDatasetPreprocessor:
    """VPN通道数据集预处理器"""
    
    def __init__(self, data_root, output_root=None):
        """
        初始化预处理器
        
        Args:
            data_root: 数据集根目录 (包含train/val/test)
            output_root: 输出目录，默认为data_root + '_balanced'
        """
        self.data_root = Path(data_root)
        self.output_root = Path(output_root) if output_root else Path(str(data_root) + '_balanced')
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('data_preprocessing.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 数据统计
        self.class_stats = {}
        self.class_weights = {}
        
        # 增强参数
        self.augmentation_config = {
            'rotation_range': 15,      # 旋转角度范围
            'brightness_range': (0.8, 1.2),  # 亮度调整范围
            'contrast_range': (0.8, 1.2),    # 对比度调整范围
            'noise_std': 0.02,         # 噪声标准差
            'blur_radius': 0.5,        # 模糊半径
            'flip_horizontal': True,   # 水平翻转
            'flip_vertical': True,     # 垂直翻转
        }
    
    def analyze_dataset(self):
        """分析数据集的类别分布"""
        self.logger.info("开始分析数据集...")
        
        splits = ['train', 'val', 'test']
        self.class_stats = {split: {} for split in splits}
        
        for split in splits:
            split_path = self.data_root / split
            if not split_path.exists():
                self.logger.warning(f"目录不存在: {split_path}")
                continue
            
            for class_dir in split_path.iterdir():
                if class_dir.is_dir():
                    class_name = class_dir.name
                    png_files = list(class_dir.glob('*.png'))
                    self.class_stats[split][class_name] = len(png_files)
        
        # 计算总体统计
        all_classes = set()
        for split_stats in self.class_stats.values():
            all_classes.update(split_stats.keys())
        
        self.total_stats = {}
        for class_name in all_classes:
            total = sum(self.class_stats[split].get(class_name, 0) for split in splits)
            self.total_stats[class_name] = total
        
        self.logger.info(f"发现 {len(all_classes)} 个类别")
        return self.class_stats, self.total_stats
    
    def compute_class_weights(self, method='balanced'):
        """计算类别权重"""
        if not self.total_stats:
            self.analyze_dataset()
        
        classes = list(self.total_stats.keys())
        samples = list(self.total_stats.values())
        
        if method == 'balanced':
            # 使用sklearn的balanced方法
            weights = compute_class_weight('balanced', classes=np.arange(len(classes)), y=np.repeat(np.arange(len(classes)), samples))
            self.class_weights = dict(zip(classes, weights))
        elif method == 'inverse':
            # 反比例权重
            max_samples = max(samples)
            self.class_weights = {cls: max_samples / count for cls, count in self.total_stats.items()}
        
        self.logger.info("类别权重计算完成")
        return self.class_weights
    
    def generate_augmented_image(self, image, augment_type='random'):
        """生成增强图像"""
        if isinstance(image, str):
            image = Image.open(image).convert('L')  # MFR图像通常是灰度图
        
        # 转换为numpy数组
        img_array = np.array(image)
        
        if augment_type == 'rotation':
            # 随机旋转
            angle = random.uniform(-self.augmentation_config['rotation_range'], 
                                 self.augmentation_config['rotation_range'])
            image = image.rotate(angle, fillcolor=0)
        
        elif augment_type == 'brightness':
            # 亮度调整
            enhancer = ImageEnhance.Brightness(image)
            factor = random.uniform(*self.augmentation_config['brightness_range'])
            image = enhancer.enhance(factor)
        
        elif augment_type == 'contrast':
            # 对比度调整
            enhancer = ImageEnhance.Contrast(image)
            factor = random.uniform(*self.augmentation_config['contrast_range'])
            image = enhancer.enhance(factor)
        
        elif augment_type == 'noise':
            # 添加高斯噪声
            img_array = np.array(image, dtype=np.float32) / 255.0
            noise = np.random.normal(0, self.augmentation_config['noise_std'], img_array.shape)
            img_array = np.clip(img_array + noise, 0, 1)
            image = Image.fromarray((img_array * 255).astype(np.uint8))
        
        elif augment_type == 'blur':
            # 轻微模糊
            image = image.filter(ImageFilter.GaussianBlur(radius=self.augmentation_config['blur_radius']))
        
        elif augment_type == 'flip_h':
            # 水平翻转
            image = image.transpose(Image.FLIP_LEFT_RIGHT)
        
        elif augment_type == 'flip_v':
            # 垂直翻转
            image = image.transpose(Image.FLIP_TOP_BOTTOM)
        
        elif augment_type == 'random':
            # 随机选择增强方法
            methods = ['rotation', 'brightness', 'contrast', 'noise', 'blur']
            if self.augmentation_config['flip_horizontal']:
                methods.append('flip_h')
            if self.augmentation_config['flip_vertical']:
                methods.append('flip_v')
            
            chosen_method = random.choice(methods)
            return self.generate_augmented_image(image, chosen_method)
        
        return image
    
    def balance_dataset_oversample(self, target_samples_per_class=None):
        """使用过采样平衡数据集"""
        if not self.class_stats:
            self.analyze_dataset()
        
        # 确定目标样本数
        if target_samples_per_class is None:
            # 使用中位数作为目标
            all_counts = [count for split_stats in self.class_stats.values() 
                         for count in split_stats.values() if count > 0]
            target_samples_per_class = int(np.median(all_counts))
        
        self.logger.info(f"目标每类样本数: {target_samples_per_class}")
        
        # 创建输出目录
        self.output_root.mkdir(exist_ok=True)
        
        for split in ['train', 'val', 'test']:
            split_input = self.data_root / split
            split_output = self.output_root / split
            split_output.mkdir(exist_ok=True)
            
            if not split_input.exists():
                continue
            
            self.logger.info(f"处理 {split} 集...")
            
            for class_name, current_count in self.class_stats[split].items():
                class_input = split_input / class_name
                class_output = split_output / class_name
                class_output.mkdir(exist_ok=True)
                
                # 获取原始文件
                original_files = list(class_input.glob('*.png'))
                
                if current_count == 0:
                    continue
                
                # 复制原始文件
                for file_path in original_files:
                    shutil.copy2(file_path, class_output / file_path.name)
                
                # 如果需要增强
                if current_count < target_samples_per_class:
                    needed_samples = target_samples_per_class - current_count
                    self.logger.info(f"类别 {class_name} 需要增强 {needed_samples} 个样本")
                    
                    # 生成增强样本
                    for i in range(needed_samples):
                        # 随机选择一个原始文件
                        source_file = random.choice(original_files)
                        
                        # 生成增强图像
                        augmented_img = self.generate_augmented_image(source_file)
                        
                        # 保存增强图像
                        base_name = source_file.stem
                        aug_name = f"{base_name}_aug_{i+1}.png"
                        augmented_img.save(class_output / aug_name)
        
        self.logger.info("过采样平衡完成")
        return self.output_root
    
    def balance_dataset_undersample(self, target_samples_per_class=None):
        """使用欠采样平衡数据集"""
        if not self.class_stats:
            self.analyze_dataset()
        
        # 确定目标样本数
        if target_samples_per_class is None:
            # 使用最小类别的样本数
            min_count = min([count for split_stats in self.class_stats.values() 
                           for count in split_stats.values() if count > 0])
            target_samples_per_class = min_count
        
        self.logger.info(f"目标每类样本数: {target_samples_per_class}")
        
        # 创建输出目录
        self.output_root.mkdir(exist_ok=True)
        
        for split in ['train', 'val', 'test']:
            split_input = self.data_root / split
            split_output = self.output_root / split
            split_output.mkdir(exist_ok=True)
            
            if not split_input.exists():
                continue
            
            self.logger.info(f"处理 {split} 集...")
            
            for class_name, current_count in self.class_stats[split].items():
                class_input = split_input / class_name
                class_output = split_output / class_name
                class_output.mkdir(exist_ok=True)
                
                # 获取原始文件
                original_files = list(class_input.glob('*.png'))
                
                if current_count == 0:
                    continue
                
                # 随机选择目标数量的文件
                if current_count <= target_samples_per_class:
                    # 如果样本数不足，全部复制
                    selected_files = original_files
                else:
                    # 随机选择
                    selected_files = random.sample(original_files, target_samples_per_class)
                
                # 复制选中的文件
                for file_path in selected_files:
                    shutil.copy2(file_path, class_output / file_path.name)
        
        self.logger.info("欠采样平衡完成")
        return self.output_root

    def create_hybrid_balanced_dataset(self, min_samples=50, max_samples=500):
        """创建混合平衡数据集（小类别过采样，大类别欠采样）"""
        if not self.class_stats:
            self.analyze_dataset()

        self.logger.info(f"混合平衡策略: 最小{min_samples}，最大{max_samples}样本")

        # 创建输出目录
        self.output_root.mkdir(exist_ok=True)

        for split in ['train', 'val', 'test']:
            split_input = self.data_root / split
            split_output = self.output_root / split
            split_output.mkdir(exist_ok=True)

            if not split_input.exists():
                continue

            self.logger.info(f"处理 {split} 集...")

            for class_name, current_count in self.class_stats[split].items():
                class_input = split_input / class_name
                class_output = split_output / class_name
                class_output.mkdir(exist_ok=True)

                # 获取原始文件
                original_files = list(class_input.glob('*.png'))

                if current_count == 0:
                    continue

                # 确定目标样本数
                if current_count < min_samples:
                    target_count = min_samples
                elif current_count > max_samples:
                    target_count = max_samples
                else:
                    target_count = current_count

                if target_count <= current_count:
                    # 欠采样或保持不变
                    if target_count == current_count:
                        selected_files = original_files
                    else:
                        selected_files = random.sample(original_files, target_count)

                    for file_path in selected_files:
                        shutil.copy2(file_path, class_output / file_path.name)
                else:
                    # 过采样
                    # 先复制所有原始文件
                    for file_path in original_files:
                        shutil.copy2(file_path, class_output / file_path.name)

                    # 生成增强样本
                    needed_samples = target_count - current_count
                    for i in range(needed_samples):
                        source_file = random.choice(original_files)
                        augmented_img = self.generate_augmented_image(source_file)

                        base_name = source_file.stem
                        aug_name = f"{base_name}_aug_{i+1}.png"
                        augmented_img.save(class_output / aug_name)

        self.logger.info("混合平衡完成")
        return self.output_root

    def generate_class_weights_config(self, output_file='class_weights.json'):
        """生成类别权重配置文件，用于YaTC训练"""
        if not self.class_weights:
            self.compute_class_weights()

        # 创建类别到索引的映射
        classes = sorted(self.class_weights.keys())
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}

        # 创建权重数组
        weights_array = [self.class_weights[cls] for cls in classes]

        config = {
            'class_to_idx': class_to_idx,
            'idx_to_class': {idx: cls for cls, idx in class_to_idx.items()},
            'class_weights': self.class_weights,
            'weights_array': weights_array,
            'num_classes': len(classes)
        }

        output_path = self.output_root / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        self.logger.info(f"类别权重配置已保存到: {output_path}")
        return output_path

    def visualize_class_distribution(self, save_path='class_distribution.png'):
        """可视化类别分布"""
        if not self.class_stats:
            self.analyze_dataset()

        # 准备数据
        classes = sorted(self.total_stats.keys())
        train_counts = [self.class_stats['train'].get(cls, 0) for cls in classes]
        val_counts = [self.class_stats['val'].get(cls, 0) for cls in classes]
        test_counts = [self.class_stats['test'].get(cls, 0) for cls in classes]

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

        # 堆叠柱状图
        x = np.arange(len(classes))
        width = 0.8

        ax1.bar(x, train_counts, width, label='训练集', alpha=0.8)
        ax1.bar(x, val_counts, width, bottom=train_counts, label='验证集', alpha=0.8)
        ax1.bar(x, test_counts, width, bottom=np.array(train_counts) + np.array(val_counts),
                label='测试集', alpha=0.8)

        ax1.set_xlabel('VPN类别')
        ax1.set_ylabel('样本数量')
        ax1.set_title('VPN通道数据集类别分布')
        ax1.set_xticks(x)
        ax1.set_xticklabels(classes, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 对数尺度图
        total_counts = [self.total_stats[cls] for cls in classes]
        ax2.bar(x, total_counts, width, alpha=0.7, color='skyblue')
        ax2.set_xlabel('VPN类别')
        ax2.set_ylabel('总样本数量 (对数尺度)')
        ax2.set_title('VPN通道数据集类别分布 (对数尺度)')
        ax2.set_xticks(x)
        ax2.set_xticklabels(classes, rotation=45, ha='right')
        ax2.set_yscale('log')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, count in enumerate(total_counts):
            ax2.text(i, count, str(count), ha='center', va='bottom')

        plt.tight_layout()

        # 保存图表
        save_full_path = self.output_root / save_path
        plt.savefig(save_full_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"类别分布图已保存到: {save_full_path}")
        return save_full_path

    def validate_augmented_images(self, sample_size=5):
        """验证增强图像的质量"""
        self.logger.info("验证增强图像质量...")

        validation_dir = self.output_root / 'validation_samples'
        validation_dir.mkdir(exist_ok=True)

        # 为每个类别生成验证样本
        for split in ['train']:  # 主要检查训练集
            split_path = self.output_root / split
            if not split_path.exists():
                continue

            for class_dir in split_path.iterdir():
                if not class_dir.is_dir():
                    continue

                class_name = class_dir.name
                class_validation_dir = validation_dir / class_name
                class_validation_dir.mkdir(exist_ok=True)

                # 获取原始和增强图像
                all_files = list(class_dir.glob('*.png'))
                original_files = [f for f in all_files if '_aug_' not in f.name]
                augmented_files = [f for f in all_files if '_aug_' in f.name]

                # 随机选择样本
                if original_files:
                    sample_original = random.sample(original_files, min(sample_size, len(original_files)))
                    for i, file_path in enumerate(sample_original):
                        shutil.copy2(file_path, class_validation_dir / f"original_{i+1}.png")

                if augmented_files:
                    sample_augmented = random.sample(augmented_files, min(sample_size, len(augmented_files)))
                    for i, file_path in enumerate(sample_augmented):
                        shutil.copy2(file_path, class_validation_dir / f"augmented_{i+1}.png")

        self.logger.info(f"验证样本已保存到: {validation_dir}")
        return validation_dir

    def generate_processing_report(self):
        """生成处理报告"""
        report = {
            'preprocessing_summary': {
                'input_directory': str(self.data_root),
                'output_directory': str(self.output_root),
                'processing_time': None,  # 可以在主函数中设置
            },
            'original_dataset': {
                'total_classes': len(self.total_stats),
                'class_distribution': self.total_stats,
                'total_samples': sum(self.total_stats.values()),
                'min_samples_per_class': min(self.total_stats.values()) if self.total_stats else 0,
                'max_samples_per_class': max(self.total_stats.values()) if self.total_stats else 0,
                'mean_samples_per_class': np.mean(list(self.total_stats.values())) if self.total_stats else 0,
                'std_samples_per_class': np.std(list(self.total_stats.values())) if self.total_stats else 0,
            },
            'class_weights': self.class_weights,
            'augmentation_config': self.augmentation_config,
        }

        # 如果有平衡后的数据集，分析平衡后的分布
        if self.output_root.exists():
            balanced_stats = self._analyze_balanced_dataset()
            report['balanced_dataset'] = balanced_stats

        # 保存报告
        report_path = self.output_root / 'preprocessing_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        # 生成Markdown报告
        self._generate_markdown_report(report)

        self.logger.info(f"处理报告已保存到: {report_path}")
        return report_path

    def _analyze_balanced_dataset(self):
        """分析平衡后的数据集"""
        balanced_stats = {'train': {}, 'val': {}, 'test': {}}

        for split in ['train', 'val', 'test']:
            split_path = self.output_root / split
            if not split_path.exists():
                continue

            for class_dir in split_path.iterdir():
                if class_dir.is_dir():
                    class_name = class_dir.name
                    png_files = list(class_dir.glob('*.png'))
                    balanced_stats[split][class_name] = len(png_files)

        # 计算总体统计
        total_balanced = {}
        for class_name in self.total_stats.keys():
            total = sum(balanced_stats[split].get(class_name, 0) for split in ['train', 'val', 'test'])
            total_balanced[class_name] = total

        return {
            'split_distribution': balanced_stats,
            'total_distribution': total_balanced,
            'total_samples': sum(total_balanced.values()),
            'min_samples_per_class': min(total_balanced.values()) if total_balanced else 0,
            'max_samples_per_class': max(total_balanced.values()) if total_balanced else 0,
            'mean_samples_per_class': np.mean(list(total_balanced.values())) if total_balanced else 0,
            'std_samples_per_class': np.std(list(total_balanced.values())) if total_balanced else 0,
        }

    def _generate_markdown_report(self, report_data):
        """生成Markdown格式的报告"""
        md_content = f"""# VPN通道数据集预处理报告

## 📊 处理概览

- **输入目录**: {report_data['preprocessing_summary']['input_directory']}
- **输出目录**: {report_data['preprocessing_summary']['output_directory']}
- **处理时间**: {report_data['preprocessing_summary']['processing_time']}

## 📈 原始数据集统计

- **总类别数**: {report_data['original_dataset']['total_classes']}
- **总样本数**: {report_data['original_dataset']['total_samples']}
- **每类最少样本**: {report_data['original_dataset']['min_samples_per_class']}
- **每类最多样本**: {report_data['original_dataset']['max_samples_per_class']}
- **每类平均样本**: {report_data['original_dataset']['mean_samples_per_class']:.1f}
- **样本数标准差**: {report_data['original_dataset']['std_samples_per_class']:.1f}

### 原始类别分布
| 类别 | 样本数 |
|------|--------|
"""

        for class_name, count in sorted(report_data['original_dataset']['class_distribution'].items()):
            md_content += f"| {class_name} | {count} |\n"

        if 'balanced_dataset' in report_data:
            md_content += f"""
## 🎯 平衡后数据集统计

- **总样本数**: {report_data['balanced_dataset']['total_samples']}
- **每类最少样本**: {report_data['balanced_dataset']['min_samples_per_class']}
- **每类最多样本**: {report_data['balanced_dataset']['max_samples_per_class']}
- **每类平均样本**: {report_data['balanced_dataset']['mean_samples_per_class']:.1f}
- **样本数标准差**: {report_data['balanced_dataset']['std_samples_per_class']:.1f}

### 平衡后类别分布
| 类别 | 样本数 | 变化 |
|------|--------|------|
"""

            for class_name in sorted(report_data['original_dataset']['class_distribution'].keys()):
                original_count = report_data['original_dataset']['class_distribution'][class_name]
                balanced_count = report_data['balanced_dataset']['total_distribution'].get(class_name, 0)
                change = balanced_count - original_count
                change_str = f"+{change}" if change > 0 else str(change)
                md_content += f"| {class_name} | {balanced_count} | {change_str} |\n"

        md_content += f"""
## ⚖️ 类别权重

| 类别 | 权重 |
|------|------|
"""

        for class_name, weight in sorted(report_data['class_weights'].items()):
            md_content += f"| {class_name} | {weight:.4f} |\n"

        md_content += f"""
## 🔧 数据增强配置

- **旋转角度范围**: ±{report_data['augmentation_config']['rotation_range']}°
- **亮度调整范围**: {report_data['augmentation_config']['brightness_range']}
- **对比度调整范围**: {report_data['augmentation_config']['contrast_range']}
- **噪声标准差**: {report_data['augmentation_config']['noise_std']}
- **模糊半径**: {report_data['augmentation_config']['blur_radius']}
- **水平翻转**: {'是' if report_data['augmentation_config']['flip_horizontal'] else '否'}
- **垂直翻转**: {'是' if report_data['augmentation_config']['flip_vertical'] else '否'}

## 📝 使用说明

### 1. 使用平衡后的数据集
平衡后的数据集位于: `{report_data['preprocessing_summary']['output_directory']}`

### 2. 使用类别权重
类别权重配置文件: `class_weights.json`

在YaTC训练中使用:
```python
import json
with open('class_weights.json', 'r') as f:
    config = json.load(f)
class_weights = torch.tensor(config['weights_array'])
```

### 3. 验证增强质量
查看验证样本: `validation_samples/`目录

---
*报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存Markdown报告
        md_path = self.output_root / 'preprocessing_report.md'
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(md_content)

        self.logger.info(f"Markdown报告已保存到: {md_path}")
        return md_path


def main():
    """主函数"""
    import argparse
    import time

    parser = argparse.ArgumentParser(description='VPN通道数据集预处理工具')
    parser.add_argument('--data_root', type=str,
                       default='D:/VPN通道识别/YaTC-main/data/VPN_MFR',
                       help='数据集根目录')
    parser.add_argument('--output_root', type=str, default=None,
                       help='输出目录，默认为data_root + "_balanced"')
    parser.add_argument('--strategy', type=str,
                       choices=['oversample', 'undersample', 'hybrid', 'weights_only'],
                       default='hybrid',
                       help='平衡策略')
    parser.add_argument('--min_samples', type=int, default=50,
                       help='混合策略的最小样本数')
    parser.add_argument('--max_samples', type=int, default=500,
                       help='混合策略的最大样本数')
    parser.add_argument('--target_samples', type=int, default=None,
                       help='过采样/欠采样的目标样本数')
    parser.add_argument('--visualize', action='store_true',
                       help='生成可视化图表')
    parser.add_argument('--validate', action='store_true',
                       help='验证增强图像质量')

    args = parser.parse_args()

    print("=" * 60)
    print("VPN通道数据集预处理工具")
    print("=" * 60)
    print(f"输入目录: {args.data_root}")
    print(f"平衡策略: {args.strategy}")
    print("=" * 60)

    # 检查输入目录
    if not os.path.exists(args.data_root):
        print(f"错误: 输入目录不存在 - {args.data_root}")
        return

    start_time = time.time()

    try:
        # 初始化预处理器
        preprocessor = VPNDatasetPreprocessor(args.data_root, args.output_root)

        # 分析原始数据集
        print("\n📊 分析原始数据集...")
        class_stats, total_stats = preprocessor.analyze_dataset()

        # 打印统计信息
        print(f"\n发现 {len(total_stats)} 个类别:")
        for class_name, count in sorted(total_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {class_name}: {count} 个样本")

        print(f"\n数据集统计:")
        print(f"  总样本数: {sum(total_stats.values())}")
        print(f"  最少样本类别: {min(total_stats.values())} 个")
        print(f"  最多样本类别: {max(total_stats.values())} 个")
        print(f"  平均样本数: {np.mean(list(total_stats.values())):.1f}")

        # 计算类别权重
        print("\n⚖️ 计算类别权重...")
        class_weights = preprocessor.compute_class_weights()

        # 执行平衡策略
        if args.strategy == 'oversample':
            print(f"\n🔄 执行过采样平衡 (目标: {args.target_samples or '自动'})...")
            output_path = preprocessor.balance_dataset_oversample(args.target_samples)
        elif args.strategy == 'undersample':
            print(f"\n🔄 执行欠采样平衡 (目标: {args.target_samples or '自动'})...")
            output_path = preprocessor.balance_dataset_undersample(args.target_samples)
        elif args.strategy == 'hybrid':
            print(f"\n🔄 执行混合平衡 (范围: {args.min_samples}-{args.max_samples})...")
            output_path = preprocessor.create_hybrid_balanced_dataset(args.min_samples, args.max_samples)
        elif args.strategy == 'weights_only':
            print("\n⚖️ 仅生成类别权重...")
            output_path = preprocessor.output_root
            preprocessor.output_root.mkdir(exist_ok=True)

        # 生成类别权重配置
        print("\n📄 生成类别权重配置...")
        weights_config_path = preprocessor.generate_class_weights_config()

        # 可视化
        if args.visualize:
            print("\n📈 生成可视化图表...")
            viz_path = preprocessor.visualize_class_distribution()

        # 验证增强图像
        if args.validate and args.strategy != 'weights_only':
            print("\n🔍 验证增强图像质量...")
            validation_path = preprocessor.validate_augmented_images()

        # 生成报告
        print("\n📋 生成处理报告...")
        processing_time = time.time() - start_time

        # 更新处理时间
        if hasattr(preprocessor, 'preprocessing_summary'):
            preprocessor.preprocessing_summary['processing_time'] = f"{processing_time:.2f} 秒"

        report_path = preprocessor.generate_processing_report()

        # 输出总结
        print("\n" + "=" * 60)
        print("✅ 预处理完成!")
        print("=" * 60)
        print(f"输出目录: {output_path}")
        print(f"处理时间: {processing_time:.2f} 秒")
        print(f"类别权重配置: {weights_config_path}")
        print(f"处理报告: {report_path}")

        if args.visualize:
            print(f"可视化图表: {viz_path}")

        if args.validate and args.strategy != 'weights_only':
            print(f"验证样本: {validation_path}")

        print("\n🎯 下一步:")
        print("1. 查看处理报告了解详细统计信息")
        print("2. 检查验证样本确认增强质量")
        print("3. 在YaTC训练中使用平衡后的数据集")
        print("4. 使用class_weights.json配置类别权重")

    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
