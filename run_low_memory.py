#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
低内存模式运行YaTC微调
专门为内存受限的环境优化
"""

import os
import sys
import subprocess
import psutil
import gc

def check_system_memory():
    """检查系统内存"""
    memory = psutil.virtual_memory()
    print(f"系统内存状态:")
    print(f"  总内存: {memory.total / 1024**3:.1f} GB")
    print(f"  可用内存: {memory.available / 1024**3:.1f} GB")
    print(f"  使用率: {memory.percent:.1f}%")
    
    if memory.available < 4 * 1024**3:  # 小于4GB可用内存
        print("⚠️  警告: 可用内存较少，建议关闭其他程序")
    
    return memory.available

def set_memory_optimizations():
    """设置内存优化环境变量"""
    print("设置内存优化参数...")
    
    # PyTorch内存优化
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128,roundup_power2_divisions:16'
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA操作，便于调试
    
    # Python内存优化
    os.environ['PYTHONHASHSEED'] = '0'
    
    # 限制OpenMP线程数
    os.environ['OMP_NUM_THREADS'] = '2'
    os.environ['MKL_NUM_THREADS'] = '2'
    
    print("✅ 内存优化参数已设置")

def run_training():
    """运行训练"""
    print("\n" + "="*60)
    print("开始低内存模式训练")
    print("="*60)
    
    # 训练参数 - 针对低内存优化
    cmd = [
        sys.executable, "fine-tune.py",
        "--batch_size", "8",  # 进一步减少batch size
        "--num_workers", "0",  # 禁用多进程
        "--no_pin_mem",  # 禁用pin memory
        "--epochs", "30",  # 减少训练轮数
        "--lr", "5e-5",  # 较小的学习率
        "--weight_decay", "0.01",
        "--early_stopping_patience", "10",
        "--output_dir", "./output_low_mem",
        "--log_dir", "./logs_low_mem",
        "--warmup_epochs", "2",  # 减少warmup
        "--save_ckpt_freq", "10",  # 减少保存频率
    ]
    
    print("训练命令:")
    print(" ".join(cmd))
    print("\n开始训练...")
    
    try:
        # 运行训练
        result = subprocess.run(cmd, cwd="YaTC-main", check=True)
        print("\n✅ 训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        return False

def cleanup_memory():
    """清理内存"""
    print("清理内存...")
    gc.collect()
    
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("✅ CUDA缓存已清理")
    except ImportError:
        pass

def main():
    """主函数"""
    print("YaTC 低内存模式训练工具")
    print("="*60)
    
    # 检查系统内存
    available_memory = check_system_memory()
    
    if available_memory < 2 * 1024**3:  # 小于2GB
        print("❌ 可用内存不足2GB，建议释放更多内存后再运行")
        return
    
    # 设置优化参数
    set_memory_optimizations()
    
    # 清理内存
    cleanup_memory()
    
    # 检查必要文件
    required_files = [
        "YaTC-main/fine-tune.py",
        "YaTC-main/data/VPN_MFR_balanced",
        "YaTC-main/output_dir/checkpoint-step150000.pth"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return
    
    print("✅ 所有必要文件检查通过")
    
    # 运行训练
    success = run_training()
    
    # 最终清理
    cleanup_memory()
    
    if success:
        print("\n🎉 训练成功完成!")
        print("检查输出目录: YaTC-main/output_low_mem/")
    else:
        print("\n💡 如果仍然遇到内存问题，请尝试:")
        print("1. 进一步减少batch_size到4或更小")
        print("2. 关闭其他占用内存的程序")
        print("3. 重启系统释放内存")
        print("4. 考虑使用CPU模式训练（较慢但内存需求更少）")

if __name__ == "__main__":
    main()
