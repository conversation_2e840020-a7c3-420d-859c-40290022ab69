#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MFR特征提取的内容
分析生成的图像是否正确
"""

import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import random
from pathlib import Path

def analyze_mfr_image(image_path):
    """分析单个MFR图像"""
    try:
        img = Image.open(image_path)
        img_array = np.array(img)
        
        stats = {
            'path': image_path,
            'size': img.size,
            'mode': img.mode,
            'min_value': img_array.min(),
            'max_value': img_array.max(),
            'mean_value': img_array.mean(),
            'std_value': img_array.std(),
            'unique_values': len(np.unique(img_array)),
            'zero_ratio': (img_array == 0).sum() / img_array.size,
            'non_zero_ratio': (img_array != 0).sum() / img_array.size
        }
        
        return stats, img_array
    except Exception as e:
        print(f"分析图像失败 {image_path}: {e}")
        return None, None

def sample_images_from_dataset(data_root, samples_per_class=3):
    """从数据集中采样图像进行分析"""
    data_root = Path(data_root)
    train_dir = data_root / 'train'
    
    if not train_dir.exists():
        print(f"训练目录不存在: {train_dir}")
        return {}
    
    samples = {}
    
    for class_dir in train_dir.iterdir():
        if class_dir.is_dir():
            class_name = class_dir.name
            png_files = list(class_dir.glob('*.png'))
            
            if png_files:
                # 随机选择几个文件
                selected = random.sample(png_files, min(samples_per_class, len(png_files)))
                samples[class_name] = selected
                print(f"类别 {class_name}: 选择了 {len(selected)} 个样本")
            else:
                print(f"类别 {class_name}: 没有PNG文件")
    
    return samples

def visualize_mfr_samples(samples, output_dir='mfr_analysis'):
    """可视化MFR样本"""
    os.makedirs(output_dir, exist_ok=True)
    
    all_stats = []
    
    for class_name, file_list in samples.items():
        print(f"\n分析类别: {class_name}")
        print("-" * 40)
        
        class_stats = []
        
        for i, file_path in enumerate(file_list):
            stats, img_array = analyze_mfr_image(file_path)
            
            if stats is None:
                continue
            
            class_stats.append(stats)
            all_stats.append(stats)
            
            # 打印统计信息
            print(f"  文件 {i+1}: {file_path.name}")
            print(f"    尺寸: {stats['size']}")
            print(f"    像素值范围: {stats['min_value']} - {stats['max_value']}")
            print(f"    平均值: {stats['mean_value']:.2f}")
            print(f"    标准差: {stats['std_value']:.2f}")
            print(f"    唯一值数量: {stats['unique_values']}")
            print(f"    零值比例: {stats['zero_ratio']:.2%}")
            print(f"    非零值比例: {stats['non_zero_ratio']:.2%}")
            
            # 保存可视化图像
            if img_array is not None:
                plt.figure(figsize=(8, 6))
                
                # 原始图像
                plt.subplot(2, 2, 1)
                plt.imshow(img_array, cmap='gray')
                plt.title(f'{class_name} - 原始MFR')
                plt.colorbar()
                
                # 直方图
                plt.subplot(2, 2, 2)
                plt.hist(img_array.flatten(), bins=50, alpha=0.7)
                plt.title('像素值分布')
                plt.xlabel('像素值')
                plt.ylabel('频次')
                
                # 行均值
                plt.subplot(2, 2, 3)
                row_means = img_array.mean(axis=1)
                plt.plot(row_means)
                plt.title('行均值')
                plt.xlabel('行')
                plt.ylabel('平均像素值')
                
                # 列均值
                plt.subplot(2, 2, 4)
                col_means = img_array.mean(axis=0)
                plt.plot(col_means)
                plt.title('列均值')
                plt.xlabel('列')
                plt.ylabel('平均像素值')
                
                plt.tight_layout()
                plt.savefig(f'{output_dir}/{class_name}_{i+1}.png', dpi=150, bbox_inches='tight')
                plt.close()
    
    return all_stats

def analyze_overall_statistics(all_stats):
    """分析整体统计信息"""
    print("\n" + "="*60)
    print("整体MFR特征分析")
    print("="*60)
    
    if not all_stats:
        print("没有有效的统计数据")
        return
    
    # 收集所有统计值
    min_values = [s['min_value'] for s in all_stats]
    max_values = [s['max_value'] for s in all_stats]
    mean_values = [s['mean_value'] for s in all_stats]
    std_values = [s['std_value'] for s in all_stats]
    unique_counts = [s['unique_values'] for s in all_stats]
    zero_ratios = [s['zero_ratio'] for s in all_stats]
    
    print(f"分析的图像数量: {len(all_stats)}")
    print(f"图像尺寸: {all_stats[0]['size']}")
    print(f"图像模式: {all_stats[0]['mode']}")
    
    print(f"\n像素值统计:")
    print(f"  最小值范围: {min(min_values)} - {max(min_values)}")
    print(f"  最大值范围: {min(max_values)} - {max(max_values)}")
    print(f"  平均值范围: {min(mean_values):.2f} - {max(mean_values):.2f}")
    print(f"  标准差范围: {min(std_values):.2f} - {max(std_values):.2f}")
    
    print(f"\n特征多样性:")
    print(f"  唯一值数量范围: {min(unique_counts)} - {max(unique_counts)}")
    print(f"  零值比例范围: {min(zero_ratios):.2%} - {max(zero_ratios):.2%}")
    
    # 检查潜在问题
    print(f"\n⚠️  潜在问题检查:")
    
    # 检查是否有太多零值
    high_zero_ratio = [s for s in all_stats if s['zero_ratio'] > 0.8]
    if high_zero_ratio:
        print(f"  - {len(high_zero_ratio)} 个图像的零值比例超过80%")
    
    # 检查是否有太少的唯一值
    low_diversity = [s for s in all_stats if s['unique_values'] < 10]
    if low_diversity:
        print(f"  - {len(low_diversity)} 个图像的唯一值少于10个")
    
    # 检查是否所有图像都很相似
    if max(mean_values) - min(mean_values) < 1:
        print(f"  - 所有图像的平均值都很相似，可能缺乏区分性")
    
    # 检查是否有异常值
    mean_of_means = np.mean(mean_values)
    std_of_means = np.std(mean_values)
    outliers = [s for s in all_stats if abs(s['mean_value'] - mean_of_means) > 2 * std_of_means]
    if outliers:
        print(f"  - {len(outliers)} 个图像可能是异常值")

def check_original_pcap_data():
    """检查原始pcap数据"""
    print("\n" + "="*60)
    print("检查原始pcap数据")
    print("="*60)
    
    original_data_path = "YaTC-main/data/VPN_MFR"
    if not os.path.exists(original_data_path):
        print(f"原始数据路径不存在: {original_data_path}")
        return
    
    # 检查几个原始类别的文件
    for class_dir in os.listdir(original_data_path):
        class_path = os.path.join(original_data_path, class_dir)
        if os.path.isdir(class_path):
            png_files = [f for f in os.listdir(class_path) if f.endswith('.png')]
            if png_files:
                # 分析第一个文件
                sample_file = os.path.join(class_path, png_files[0])
                stats, img_array = analyze_mfr_image(sample_file)
                if stats:
                    print(f"类别 {class_dir}: 平均值={stats['mean_value']:.2f}, "
                          f"零值比例={stats['zero_ratio']:.2%}, "
                          f"唯一值={stats['unique_values']}")

def main():
    """主函数"""
    print("MFR特征提取分析工具")
    print("="*60)
    
    # 设置随机种子
    random.seed(42)
    
    # 检查原始数据
    check_original_pcap_data()
    
    # 分析平衡后的数据集
    data_root = "YaTC-main/data/VPN_MFR_balanced"
    
    if not os.path.exists(data_root):
        print(f"数据集路径不存在: {data_root}")
        return
    
    # 采样图像
    print(f"\n从数据集采样图像: {data_root}")
    samples = sample_images_from_dataset(data_root, samples_per_class=2)
    
    if not samples:
        print("没有找到样本图像")
        return
    
    # 分析和可视化
    print(f"\n开始分析MFR特征...")
    all_stats = visualize_mfr_samples(samples)
    
    # 整体分析
    analyze_overall_statistics(all_stats)
    
    print(f"\n✅ 分析完成!")
    print(f"可视化结果保存在: mfr_analysis/ 目录")
    print(f"\n💡 如果发现问题:")
    print(f"1. 零值比例过高 -> 检查pcap文件质量")
    print(f"2. 唯一值太少 -> 检查MFR生成算法")
    print(f"3. 所有图像相似 -> 检查数据集多样性")

if __name__ == "__main__":
    main()
