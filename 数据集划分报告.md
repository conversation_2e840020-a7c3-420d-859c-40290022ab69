# VPN通道数据集划分报告

## 📊 数据集概览

### 原始数据统计
- **总文件数**: 4,354 个MFR图像文件
- **类别数量**: 14 个VPN通道类别
- **文件格式**: PNG图像 (40x40像素)
- **数据来源**: VPN通道数据集经过MFR特征提取

### 类别分布详情
| 类别名称 | 文件数量 | 占比 |
|---------|---------|------|
| Clash_DoH | 1,200 | 27.5% |
| IPSec | 737 | 16.9% |
| OpenVPN | 668 | 15.3% |
| CiscoVPN | 655 | 15.0% |
| Wireguard | 537 | 12.3% |
| L2TP | 300 | 6.9% |
| SSLVPN | 218 | 5.0% |
| Vmess | 15 | 0.3% |
| Vless | 10 | 0.2% |
| OpenVPN_Vmess | 4 | 0.1% |
| Wireguard_SSH | 3 | 0.1% |
| SS_Vmess | 3 | 0.1% |
| OpenVPN_Vless | 2 | 0.0% |
| SSR_Vmess | 2 | 0.0% |

## 🎯 划分策略

### 划分比例
- **训练集**: 70% (3,042 个文件)
- **验证集**: 15% (652 个文件)
- **测试集**: 15% (660 个文件)

### 划分方法
- **随机划分**: 每个类别内部随机打乱后按比例分配
- **保持类别平衡**: 确保每个子集都包含所有14个类别
- **文件复制**: 保留原始文件，复制到各子集目录

## 📁 输出目录结构

```
D:/VPN通道识别/YaTC-main/data/VPN_MFR/
├── train/          # 训练集 (70%)
│   ├── CiscoVPN/
│   ├── Clash_DoH/
│   ├── IPSec/
│   ├── L2TP/
│   ├── OpenVPN/
│   ├── OpenVPN_Vless/
│   ├── OpenVPN_Vmess/
│   ├── SSLVPN/
│   ├── SSR_Vmess/
│   ├── SS_Vmess/
│   ├── Vless/
│   ├── Vmess/
│   ├── Wireguard/
│   └── Wireguard_SSH/
├── val/            # 验证集 (15%)
│   └── [相同的14个类别目录]
└── test/           # 测试集 (15%)
    └── [相同的14个类别目录]
```

## 📈 各类别划分详情

### 大类别 (>500个文件)
| 类别 | 训练集 | 验证集 | 测试集 | 总计 |
|------|--------|--------|--------|------|
| Clash_DoH | 840 (70.0%) | 180 (15.0%) | 180 (15.0%) | 1,200 |
| IPSec | 515 (69.9%) | 111 (15.1%) | 111 (15.1%) | 737 |
| OpenVPN | 467 (69.9%) | 100 (15.0%) | 101 (15.1%) | 668 |
| CiscoVPN | 458 (69.9%) | 98 (15.0%) | 99 (15.1%) | 655 |
| Wireguard | 375 (69.8%) | 81 (15.1%) | 81 (15.1%) | 537 |

### 中等类别 (100-500个文件)
| 类别 | 训练集 | 验证集 | 测试集 | 总计 |
|------|--------|--------|--------|------|
| L2TP | 210 (70.0%) | 45 (15.0%) | 45 (15.0%) | 300 |
| SSLVPN | 152 (69.7%) | 33 (15.1%) | 33 (15.1%) | 218 |

### 小类别 (<100个文件)
| 类别 | 训练集 | 验证集 | 测试集 | 总计 | 备注 |
|------|--------|--------|--------|------|------|
| Vmess | 10 (66.7%) | 2 (13.3%) | 3 (20.0%) | 15 | 样本较少 |
| Vless | 7 (70.0%) | 1 (10.0%) | 2 (20.0%) | 10 | 样本较少 |
| OpenVPN_Vmess | 2 (50.0%) | 1 (25.0%) | 1 (25.0%) | 4 | 样本极少 |
| Wireguard_SSH | 2 (66.7%) | 0 (0.0%) | 1 (33.3%) | 3 | 样本极少 |
| SS_Vmess | 2 (66.7%) | 0 (0.0%) | 1 (33.3%) | 3 | 样本极少 |
| OpenVPN_Vless | 1 (50.0%) | 0 (0.0%) | 1 (50.0%) | 2 | 样本极少 |
| SSR_Vmess | 1 (50.0%) | 0 (0.0%) | 1 (50.0%) | 2 | 样本极少 |

## ⚠️ 注意事项

### 数据不平衡问题
1. **严重不平衡**: 最大类别(Clash_DoH: 1,200)与最小类别(OpenVPN_Vless, SSR_Vmess: 2)相差600倍
2. **小样本类别**: 7个类别的样本数少于20个，可能影响模型训练效果
3. **建议**: 考虑使用数据增强、重采样或加权损失函数来处理不平衡问题

### 验证集分配
- 对于样本极少的类别(≤3个样本)，验证集可能为0，这是正常的数学结果
- 这些类别主要依赖训练集和测试集进行评估

## ✅ 质量验证

### 完整性检查
- ✅ 所有14个类别在三个子集中都存在对应目录
- ✅ 文件总数匹配: 3,042 + 652 + 660 = 4,354
- ✅ 比例准确: 69.9% + 15.0% + 15.2% ≈ 100%

### 文件验证
- ✅ 所有文件都是有效的PNG格式
- ✅ 图像尺寸统一为40x40像素
- ✅ 文件名保持原始命名规范

## 🎯 使用建议

### 模型训练
1. **处理数据不平衡**: 使用类别权重或重采样技术
2. **验证策略**: 对于小样本类别，考虑使用交叉验证
3. **评估指标**: 使用平衡准确率、F1-score等指标，避免被大类别主导

### 数据增强
1. **小样本类别**: 对样本数<100的类别进行数据增强
2. **增强方法**: 旋转、翻转、噪声添加等(适用于MFR图像)
3. **保持特征**: 确保增强不破坏MFR的关键特征

### 后续处理
1. **类别合并**: 考虑将相似的小类别合并(如各种Vmess变体)
2. **重新采样**: 收集更多小类别的数据样本
3. **分层训练**: 先训练大类别，再微调小类别

## 📋 文件清单

### 生成的文件
- `train/`: 训练集目录及文件
- `val/`: 验证集目录及文件  
- `test/`: 测试集目录及文件
- `数据集划分报告.md`: 本报告文件

### 保留的文件
- 原始类别目录及MFR图像文件(已保留)

---

**数据集划分完成时间**: 2025年8月5日  
**处理工具**: YaTC VPN通道数据集划分工具  
**总处理时间**: 约5秒  
**状态**: ✅ 成功完成
