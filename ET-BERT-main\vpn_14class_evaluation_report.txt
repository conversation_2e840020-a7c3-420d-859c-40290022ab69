VPN隧道分类器真实评估报告
==================================================
评估时间: 2025-08-05 17:22:31
模型文件: models/vpn_classifier_14class.bin
测试数据: results/vpn_tunnels_class14/test_dataset_14class.tsv

1. 流量总量
   总样本数: 2942

2. 隧道种类数
   总隧道类型: 14
   单一隧道: 10
   嵌套隧道: 4

3. 识别准确率
   总体准确率: 0.8817 (88.17%)

4. 嵌套隧道识别准确率
   嵌套隧道准确率: 0.8820 (88.20%)
   嵌套隧道类型包括:
     - OpenVPN+Vless
     - OpenVPN+Vmess
     - SS+Vmess
     - Wireguard+SSH

5. 评估详情
   正确预测数: 2593
   错误预测数: 348
   嵌套隧道样本数: 942

6. 原始评估输出 (完整)
----------------------------------------
   Calculating class weights from training data...
   Total training samples: 2942
   Actual labels found: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]
     Label 0: 200 samples (6.80%)
     Label 1: 200 samples (6.80%)
     Label 2: 200 samples (6.80%)
     Label 3: 200 samples (6.80%)
     Label 4: 200 samples (6.80%)
     Label 5: 200 samples (6.80%)
     Label 6: 200 samples (6.80%)
     Label 7: 342 samples (11.62%)
     Label 8: 200 samples (6.80%)
     Label 9: 200 samples (6.80%)
     Label 10: 200 samples (6.80%)
     Label 11: 200 samples (6.80%)
     Label 12: 200 samples (6.80%)
     Label 13: 200 samples (6.80%)
   Label mapping: {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, 10: 10, 11: 11, 12: 12, 13: 13}
   Applied class weights to loss function:
     Label 0 (index 0): weight=1.05
     Label 1 (index 1): weight=1.05
     Label 2 (index 2): weight=1.05
     Label 3 (index 3): weight=1.05
     Label 4 (index 4): weight=1.05
     Label 5 (index 5): weight=1.05
     Label 6 (index 6): weight=1.05
     Label 7 (index 7): weight=0.61
     Label 8 (index 8): weight=1.05
     Label 9 (index 9): weight=1.05
     Label 10 (index 10): weight=1.05
     Label 11 (index 11): weight=1.05
     Label 12 (index 12): weight=1.05
     Label 13 (index 13): weight=1.05
   Updated labels_num to 14 based on actual data
   ***** Running training *****
     Num examples = 2942
     Num Epochs = 0
     Batch size = 16
     Num labels = 14
   ***** Training finished *****
   Loading best model from epoch 0 for final evaluation...
   Warning: Best model file not found. Evaluating the last state of the model.
   ***** Final evaluation on Test Set *****
   Acc. (Correct/Total): 0.8817 (2594/2942)
   Confusion matrix (Rows: True, Columns: Predicted):
   tensor([[200,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0],
           [ 15, 182,   0,   0,   0,   0,   3,   0,   0,   0,   0,   0,   0,   0],
           [  0,   0, 180,   0,   7,   0,  12,   0,   0,   0,   1,   0,   0,   0],
           [  0,   0,   0, 192,   0,   0,   0,   0,   0,   0,   6,   0,   2,   0],
           [  0,  20,  57,   0,  70,   0,  52,   0,   0,   0,   1,   0,   0,   0],
           [  0,   0,   0,   0,   0, 195,   1,   3,   0,   0,   0,   1,   0,   0],
           [  0,  64,   7,   0,   5,   0, 124,   0,   0,   0,   0,   0,   0,   0],
           [  0,   0,   0,   0,   0,   0,   0, 342,   0,   0,   0,   0,   0,   0],
           [  0,   0,   0,   0,   0,   1,   0,   0, 185,   4,   6,   4,   0,   0],
           [  0,   0,   0,   0,   0,   0,   0,   0,   0, 200,   0,   0,   0,   0],
           [  0,   0,   1,   0,   0,   1,   0,   0,   0,   0, 191,   7,   0,   0],
           [  0,   0,   0,   0,   0,   1,   1,   5,   4,   8,   7, 153,   0,  21],
           [  0,   0,   0,   0,   0,   0,   1,   3,   0,   0,   0,   0, 196,   0],
           [  0,   0,   1,   0,   0,   0,   0,   6,   0,   0,   2,   7,   0, 184]])
   Class  0 (OpenVPN+Vless  ): P=0.9302, R=1.0000, F1=0.9639
   Class  1 (OpenVPN+Vmess  ): P=0.6842, R=0.9100, F1=0.7811
   Class  2 (SS+Vmess       ): P=0.7317, R=0.9000, F1=0.8072
   Class  3 (SSLVPN         ): P=1.0000, R=0.9600, F1=0.9796
   Class  4 (SSR+Vmess      ): P=0.8537, R=0.3500, F1=0.4965
   Class  5 (Vless          ): P=0.9848, R=0.9750, F1=0.9799
   Class  6 (Vmess          ): P=0.6392, R=0.6200, F1=0.6294
   Class  7 (Wireguard+SSH  ): P=0.9526, R=1.0000, F1=0.9757
   Class  8 (ciscovpn       ): P=0.9788, R=0.9250, F1=0.9512
   Class  9 (clash_doh      ): P=0.9434, R=1.0000, F1=0.9709
   Class 10 (ipsec          ): P=0.8925, R=0.9550, F1=0.9227
   Class 11 (l2tp           ): P=0.8895, R=0.7650, F1=0.8226
   Class 12 (openvpn        ): P=0.9899, R=0.9800, F1=0.9849
   Class 13 (wireguard      ): P=0.8976, R=0.9200, F1=0.9086

==================================================
真实评估报告生成完成