import argparse
import datetime
import json
import numpy as np
import os
import time
from pathlib import Path

import torch
import torch.backends.cudnn as cudnn
from torch.utils.tensorboard import SummaryWriter

import timm

# assert timm.__version__ == "0.3.2"  # version check
from timm.models.layers import trunc_normal_
from timm.data.mixup import Mixup
from timm.loss import LabelSmoothingCrossEntropy, SoftTargetCrossEntropy

import os
import PIL
import shutil

from torchvision import datasets, transforms

import util.lr_decay as lrd
import util.misc as misc
from util.pos_embed import interpolate_pos_embed
from util.misc import NativeScalerWithGradNormCount as NativeScaler

import models_YaTC

from engine import train_one_epoch, evaluate


def get_args_parser():
    parser = argparse.ArgumentParser('YaTC fine-tuning for traffic classification', add_help=False)
    # 64
    parser.add_argument('--batch_size', default=16, type=int,
                        help='Batch size per GPU (effective batch size is batch_size * accum_iter * # gpus')
    parser.add_argument('--epochs', default=50, type=int)
    parser.add_argument('--accum_iter', default=1, type=int,
                        help='Accumulate gradient iterations (for increasing the effective batch size under memory constraints)')

    # Model parameters
    parser.add_argument('--model', default='TraFormer_YaTC', type=str, metavar='MODEL',
                        help='Name of model to train')

    parser.add_argument('--input_size', default=40, type=int,
                        help='images input size')

    parser.add_argument('--drop_path', type=float, default=0.1, metavar='PCT',
                        help='Drop path rate (default: 0.1)')

    # Optimizer parameters
    parser.add_argument('--clip_grad', type=float, default=None, metavar='NORM',
                        help='Clip gradient norm (default: None, no clipping)')
    parser.add_argument('--weight_decay', type=float, default=0.05,
                        help='weight decay (default: 0.05)')

    parser.add_argument('--lr', type=float, default=None, metavar='LR',
                        help='learning rate (absolute lr)')
    parser.add_argument('--blr', type=float, default=2e-3, metavar='LR',
                        help='base learning rate: absolute_lr = base_lr * total_batch_size / 256')
    parser.add_argument('--layer_decay', type=float, default=0.75,
                        help='layer-wise lr decay from ELECTRA/BEiT')

    parser.add_argument('--min_lr', type=float, default=1e-6, metavar='LR',
                        help='lower lr bound for cyclic schedulers that hit 0')
#20
    parser.add_argument('--warmup_epochs', type=int, default=20, metavar='N',
                        help='epochs to warmup LR')

    # Augmentation parameters
    parser.add_argument('--color_jitter', type=float, default=None, metavar='PCT',
                        help='Color jitter factor (enabled only when not using Auto/RandAug)')
    parser.add_argument('--aa', type=str, default='rand-m9-mstd0.5-inc1', metavar='NAME',
                        help='Use AutoAugment policy. "v0" or "original". " + "(default: rand-m9-mstd0.5-inc1)'),
    parser.add_argument('--smoothing', type=float, default=0.1,
                        help='Label smoothing (default: 0.1)')

    # * Random Erase params
    parser.add_argument('--reprob', type=float, default=0.25, metavar='PCT',
                        help='Random erase prob (default: 0.25)')
    parser.add_argument('--remode', type=str, default='pixel',
                        help='Random erase mode (default: "pixel")')
    parser.add_argument('--recount', type=int, default=1,
                        help='Random erase count (default: 1)')
    parser.add_argument('--resplit', action='store_true', default=False,
                        help='Do not random erase first (clean) augmentation split')

    # * Mixup params
    parser.add_argument('--mixup', type=float, default=0,
                        help='mixup alpha, mixup enabled if > 0.')
    parser.add_argument('--cutmix', type=float, default=0,
                        help='cutmix alpha, cutmix enabled if > 0.')
    parser.add_argument('--cutmix_minmax', type=float, nargs='+', default=None,
                        help='cutmix min/max ratio, overrides alpha and enables cutmix if set (default: None)')
    parser.add_argument('--mixup_prob', type=float, default=1.0,
                        help='Probability of performing mixup or cutmix when either/both is enabled')
    parser.add_argument('--mixup_switch_prob', type=float, default=0.5,
                        help='Probability of switching to cutmix when both mixup and cutmix enabled')
    parser.add_argument('--mixup_mode', type=str, default='batch',
                        help='How to apply mixup/cutmix params. Per "batch", "pair", or "elem"')
    parser.add_argument('--finetune', default='./output_dir/checkpoint-step150000.pth',
                        help='finetune from checkpoint')
    parser.add_argument('--data_path', default='./data/VPN_MFR_balanced', type=str,
                        help='dataset path')
    parser.add_argument('--nb_classes', default=14, type=int,
                        help='number of the classification types')
    parser.add_argument('--class_weights_file', default='class_weights.json', type=str,
                        help='class weights configuration file for handling data imbalance')
    parser.add_argument('--use_class_weights', action='store_true', default=True,
                        help='use class weights to handle data imbalance')
    parser.add_argument('--early_stopping_patience', default=15, type=int,
                        help='early stopping patience')
    parser.add_argument('--output_dir', default='',
                        help='path where to save, empty for no saving')
    parser.add_argument('--log_dir', default='./output_dir',
                        help='path where to tensorboard log')
    parser.add_argument('--device', default='cuda',
                        help='device to use for training / testing')
    parser.add_argument('--seed', default=0, type=int)
    parser.add_argument('--resume', default='',
                        help='resume from checkpoint')

    parser.add_argument('--start_epoch', default=0, type=int, metavar='N',
                        help='start epoch')
    parser.add_argument('--eval', action='store_true',
                        help='Perform evaluation only')
    parser.add_argument('--dist_eval', action='store_true', default=False,
                        help='Enabling distributed evaluation (recommended during training for faster monitor')
    parser.add_argument('--num_workers', default=2, type=int)  # 减少worker数量
    parser.add_argument('--pin_mem', action='store_true',
                        help='Pin CPU memory in DataLoader for more efficient (sometimes) transfer to GPU.')
    parser.add_argument('--no_pin_mem', action='store_false', dest='pin_mem')
    parser.set_defaults(pin_mem=False)  # 禁用pin_memory以节省内存

    # distributed training parameters
    parser.add_argument('--world_size', default=1, type=int,
                        help='number of distributed processes')
    parser.add_argument('--local_rank', default=-1, type=int)
    parser.add_argument('--dist_on_itp', action='store_true')
    parser.add_argument('--dist_url', default='env://',
                        help='url used to set up distributed training')

    return parser

def build_dataset(split, args):
    """
    构建数据集
    Args:
        split: 'train', 'val', 或 'test'
        args: 参数配置
    """
    mean = [0.5]
    std = [0.5]

    # 训练时使用数据增强
    if split == 'train':
        transform = transforms.Compose([
            transforms.Grayscale(num_output_channels=1),
            transforms.RandomRotation(degrees=10),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])
    else:
        transform = transforms.Compose([
            transforms.Grayscale(num_output_channels=1),
            transforms.ToTensor(),
            transforms.Normalize(mean, std),
        ])

    root = os.path.join(args.data_path, split)

    # 检查目录是否存在
    if not os.path.exists(root):
        raise FileNotFoundError(f"Dataset directory not found: {root}")

    # 检查是否有空的类别目录，如果有则临时移除
    class_dirs = [d for d in os.listdir(root) if os.path.isdir(os.path.join(root, d))]
    empty_classes = []
    moved_dirs = {}  # 记录移动的目录

    for class_dir in class_dirs:
        class_path = os.path.join(root, class_dir)
        files = [f for f in os.listdir(class_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        if len(files) == 0:
            empty_classes.append(class_dir)

    if empty_classes:
        print(f"Warning: Found empty class directories in {split} set: {empty_classes}")
        print("These classes will be temporarily moved to avoid ImageFolder errors.")

        # 创建临时目录来存放空目录
        temp_root = root + "_temp_empty"
        os.makedirs(temp_root, exist_ok=True)

        # 移动空目录到临时位置
        for empty_class in empty_classes:
            empty_path = os.path.join(root, empty_class)
            temp_path = os.path.join(temp_root, empty_class)
            if os.path.exists(empty_path):
                shutil.move(empty_path, temp_path)
                moved_dirs[empty_class] = temp_path

    try:
        dataset = datasets.ImageFolder(root, transform=transform)
        print(f"{split} dataset: {dataset}")
        print(f"Classes: {dataset.classes}")
        print(f"Number of samples: {len(dataset)}")

        return dataset

    finally:
        # 恢复移动的空目录
        if moved_dirs:
            for empty_class, temp_path in moved_dirs.items():
                original_path = os.path.join(root, empty_class)
                if os.path.exists(temp_path):
                    shutil.move(temp_path, original_path)

            # 删除临时根目录
            temp_root = root + "_temp_empty"
            if os.path.exists(temp_root):
                try:
                    os.rmdir(temp_root)
                except:
                    pass

def load_class_weights(weights_file, device):
    """加载类别权重"""
    try:
        with open(weights_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        weights = torch.tensor(config['weights_array'], dtype=torch.float32).to(device)
        print(f"Loaded class weights from {weights_file}")
        print(f"Weights: {weights}")
        return weights
    except Exception as e:
        print(f"Warning: Could not load class weights from {weights_file}: {e}")
        return None

def main(args):
    misc.init_distributed_mode(args)

    print('job dir: {}'.format(os.path.dirname(os.path.realpath(__file__))))
    print("{}".format(args).replace(', ', ',\n'))

    device = torch.device(args.device)

    # fix the seed for reproducibility
    seed = args.seed + misc.get_rank()
    torch.manual_seed(seed)
    np.random.seed(seed)

    cudnn.benchmark = True

    dataset_train = build_dataset('train', args=args)
    dataset_val = build_dataset('val', args=args)
    dataset_test = build_dataset('test', args=args)

    labels = dataset_val.classes
    print(f"Class labels: {labels}")

    if True:  # args.distributed:
        num_tasks = misc.get_world_size()
        global_rank = misc.get_rank()
        sampler_train = torch.utils.data.DistributedSampler(
            dataset_train, num_replicas=num_tasks, rank=global_rank, shuffle=True
        )
        print("Sampler_train = %s" % str(sampler_train))
        if args.dist_eval:
            if len(dataset_val) % num_tasks != 0:
                print('Warning: Enabling distributed evaluation with an eval dataset not divisible by process number. '
                      'This will slightly alter validation results as extra duplicate entries are added to achieve '
                      'equal num of samples per-process.')
            sampler_val = torch.utils.data.DistributedSampler(
                dataset_val, num_replicas=num_tasks, rank=global_rank,
                shuffle=True)  # shuffle=True to reduce monitor bias
            sampler_test = torch.utils.data.DistributedSampler(
                dataset_test, num_replicas=num_tasks, rank=global_rank,
                shuffle=False)
        else:
            sampler_val = torch.utils.data.SequentialSampler(dataset_val)
            sampler_test = torch.utils.data.SequentialSampler(dataset_test)
    else:
        sampler_train = torch.utils.data.RandomSampler(dataset_train)
        sampler_val = torch.utils.data.SequentialSampler(dataset_val)
        sampler_test = torch.utils.data.SequentialSampler(dataset_test)

    if global_rank == 0 and args.log_dir is not None and not args.eval:
        os.makedirs(args.log_dir, exist_ok=True)
        log_writer = SummaryWriter(log_dir=args.log_dir)
    else:
        log_writer = None

    data_loader_train = torch.utils.data.DataLoader(
        dataset_train, sampler=sampler_train,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        pin_memory=args.pin_mem,
        drop_last=True,
    )

    data_loader_val = torch.utils.data.DataLoader(
        dataset_val, sampler=sampler_val,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        pin_memory=args.pin_mem,
        drop_last=False
    )

    data_loader_test = torch.utils.data.DataLoader(
        dataset_test, sampler=sampler_test,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        pin_memory=args.pin_mem,
        drop_last=False
    )

    mixup_fn = None
    mixup_active = args.mixup > 0 or args.cutmix > 0. or args.cutmix_minmax is not None
    if mixup_active:
        print("Mixup is activated!")
        mixup_fn = Mixup(
            mixup_alpha=args.mixup, cutmix_alpha=args.cutmix, cutmix_minmax=args.cutmix_minmax,
            prob=args.mixup_prob, switch_prob=args.mixup_switch_prob, mode=args.mixup_mode,
            label_smoothing=args.smoothing, num_classes=args.nb_classes)

    model = models_YaTC.__dict__[args.model](
        num_classes=args.nb_classes,
        drop_path_rate=args.drop_path,
    )

    if args.finetune and not args.eval:
        checkpoint = torch.load(args.finetune, map_location='cpu')
        print("Load pre-trained checkpoint from: %s" % args.finetune)
        checkpoint_model = checkpoint['model']
        state_dict = model.state_dict()
        for k in ['head.weight', 'head.bias']:
            if k in checkpoint_model and checkpoint_model[k].shape != state_dict[k].shape:
                print(f"Removing key {k} from pretrained checkpoint")
                del checkpoint_model[k]

        # interpolate position embedding
        interpolate_pos_embed(model, checkpoint_model)

        # load pre-trained model
        msg = model.load_state_dict(checkpoint_model, strict=False)
        print(msg)

        # manually initialize fc layer
        trunc_normal_(model.head.weight, std=2e-5)

    model.to(device)

    model_without_ddp = model
    n_parameters = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print("Model = %s" % str(model_without_ddp))
    print('number of params (M): %.2f' % (n_parameters / 1.e6))

    eff_batch_size = args.batch_size * args.accum_iter * misc.get_world_size()

    if args.lr is None:  # only base_lr is specified
        args.lr = args.blr * eff_batch_size / 256

    print("base lr: %.2e" % (args.lr * 256 / eff_batch_size))
    print("actual lr: %.2e" % args.lr)

    print("accumulate grad iterations: %d" % args.accum_iter)
    print("effective batch size: %d" % eff_batch_size)

    if args.distributed:
        model = torch.nn.parallel.DistributedDataParallel(model, device_ids=[args.gpu])
        model_without_ddp = model.module

    # build optimizer with layer-wise lr decay (lrd)
    param_groups = lrd.param_groups_lrd(model_without_ddp, args.weight_decay,
                                        no_weight_decay_list=model_without_ddp.no_weight_decay(),
                                        layer_decay=args.layer_decay
                                        )
    optimizer = torch.optim.AdamW(param_groups, lr=args.lr)
    loss_scaler = NativeScaler()

    # 加载类别权重
    class_weights = None
    if args.use_class_weights:
        weights_file = os.path.join(args.data_path, args.class_weights_file)
        class_weights = load_class_weights(weights_file, device)

    if mixup_fn is not None:
        # smoothing is handled with mixup label transform
        criterion = SoftTargetCrossEntropy()
    elif args.smoothing > 0.:
        # LabelSmoothingCrossEntropy doesn't support weight parameter
        # Use standard CrossEntropyLoss with weights instead
        if class_weights is not None:
            print("Warning: Using CrossEntropyLoss with weights instead of LabelSmoothingCrossEntropy")
            criterion = torch.nn.CrossEntropyLoss(weight=class_weights, label_smoothing=args.smoothing)
        else:
            criterion = LabelSmoothingCrossEntropy(smoothing=args.smoothing)
    else:
        if class_weights is not None:
            criterion = torch.nn.CrossEntropyLoss(weight=class_weights)
        else:
            criterion = torch.nn.CrossEntropyLoss()

    print("criterion = %s" % str(criterion))

    misc.load_model(args=args, model_without_ddp=model_without_ddp, optimizer=optimizer, loss_scaler=loss_scaler)

    if args.eval:
        print("Evaluating on validation set...")
        val_stats = evaluate(data_loader_val, model, device)
        print(f"Validation - Accuracy: {val_stats['acc1']:.4f}, F1: {val_stats['macro_f1']:.4f}")

        print("Evaluating on test set...")
        test_stats = evaluate(data_loader_test, model, device)
        print(f"Test - Accuracy: {test_stats['acc1']:.4f}, F1: {test_stats['macro_f1']:.4f}")
        exit(0)

    print(f"Start training for {args.epochs} epochs")
    start_time = time.time()
    max_accuracy = 0.0
    max_f1 = 0.0
    best_epoch = 0
    patience_counter = 0

    for epoch in range(args.start_epoch, args.epochs):
        if args.distributed:
            data_loader_train.sampler.set_epoch(epoch)
        train_stats = train_one_epoch(
            model, criterion, data_loader_train,
            optimizer, device, epoch, loss_scaler,
            args.clip_grad, mixup_fn,
            log_writer=log_writer,
            args=args
        )

        # 在验证集上评估
        val_stats = evaluate(data_loader_val, model, device)

        print(f"Epoch {epoch}:")
        print(f"  Validation - Accuracy: {val_stats['acc1']:.4f}, F1: {val_stats['macro_f1']:.4f}")

        # 更新最佳指标
        current_f1 = val_stats["macro_f1"]
        if current_f1 > max_f1:
            max_f1 = current_f1
            max_accuracy = val_stats["acc1"]
            best_epoch = epoch
            patience_counter = 0

            # 保存最佳模型
            misc.save_model(
                args=args, model=model, model_without_ddp=model_without_ddp,
                optimizer=optimizer, loss_scaler=loss_scaler, epoch=epoch,
                name="best"
            )
            print(f"  New best model saved! F1: {max_f1:.4f}, Accuracy: {max_accuracy:.4f}")
        else:
            patience_counter += 1
            print(f"  No improvement. Patience: {patience_counter}/{args.early_stopping_patience}")

        print(f"  Best so far - Epoch: {best_epoch}, F1: {max_f1:.4f}, Accuracy: {max_accuracy:.4f}")

        # 早停检查
        if patience_counter >= args.early_stopping_patience:
            print(f"Early stopping triggered after {patience_counter} epochs without improvement")
            break

        log_stats = {**{f'train_{k}': v for k, v in train_stats.items()},
                     **{f'val_{k}': v for k, v in val_stats.items()},
                     'epoch': epoch,
                     'n_parameters': n_parameters,
                     'best_epoch': best_epoch,
                     'max_f1': max_f1,
                     'max_accuracy': max_accuracy}

        # 记录到TensorBoard
        if log_writer is not None:
            log_writer.add_scalar('val_accuracy', val_stats['acc1'], epoch)
            log_writer.add_scalar('val_f1', val_stats['macro_f1'], epoch)
            log_writer.add_scalar('val_loss', val_stats['loss'], epoch)

    # 训练结束后在测试集上评估最佳模型
    print("\n" + "="*60)
    print("Training completed! Evaluating best model on test set...")
    print("="*60)

    # 加载最佳模型
    best_checkpoint_path = os.path.join(args.output_dir, 'checkpoint-best.pth')
    if os.path.exists(best_checkpoint_path):
        print(f"Loading best model from {best_checkpoint_path}")
        checkpoint = torch.load(best_checkpoint_path, map_location='cpu')
        model_without_ddp.load_state_dict(checkpoint['model'])

    # 在测试集上评估
    final_test_stats = evaluate(data_loader_test, model, device)

    print(f"\nFinal Test Results:")
    print(f"  Accuracy: {final_test_stats['acc1']:.4f}")
    print(f"  Macro F1: {final_test_stats['macro_f1']:.4f}")
    print(f"  Macro Precision: {final_test_stats['macro_pre']:.4f}")
    print(f"  Macro Recall: {final_test_stats['macro_rec']:.4f}")

    # 保存最终结果
    final_results = {
        'best_epoch': best_epoch,
        'best_val_f1': max_f1,
        'best_val_accuracy': max_accuracy,
        'final_test_accuracy': final_test_stats['acc1'],
        'final_test_f1': final_test_stats['macro_f1'],
        'final_test_precision': final_test_stats['macro_pre'],
        'final_test_recall': final_test_stats['macro_rec'],
        'training_time': str(datetime.timedelta(seconds=int(time.time() - start_time))),
        'class_names': labels
    }

    results_file = os.path.join(args.output_dir, 'final_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, indent=2, ensure_ascii=False, default=str)

    print(f"\nResults saved to: {results_file}")

    total_time = time.time() - start_time
    total_time_str = str(datetime.timedelta(seconds=int(total_time)))
    print(f'Total training time: {total_time_str}')


if __name__ == '__main__':
    args = get_args_parser()
    args = args.parse_args()
    if args.output_dir:
        Path(args.output_dir).mkdir(parents=True, exist_ok=True)
    main(args)
