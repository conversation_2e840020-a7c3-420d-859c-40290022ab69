
import os
from collections import Counter

def create_vocab_from_tsv(train_path, output_path):
    """
    Reads a TSV file, extracts tokens from the 'text_a' column,
    and creates a vocabulary file.
    """
    token_counts = Counter()
    columns = {}
    print(f"Reading training data from: {train_path}")

    try:
        with open(train_path, "r", encoding="utf-8") as f:
            for i, line in enumerate(f):
                if i == 0:
                    # Parse header
                    for j, column_name in enumerate(line.strip().split("\t")):
                        columns[column_name] = j
                    if "text_a" not in columns:
                        print("Error: 'text_a' column not found in the header.")
                        return
                    continue
                
                parts = line.strip().split("\t")
                if len(parts) > columns["text_a"]:
                    text = parts[columns["text_a"]]
                    tokens = text.split(" ")
                    token_counts.update(tokens)

    except FileNotFoundError:
        print(f"Error: The file '{train_path}' was not found.")
        return
    except Exception as e:
        print(f"An error occurred: {e}")
        return

    # BERT special tokens
    special_tokens = ["[PAD]", "[UNK]", "[CLS]", "[SEP]", "[MASK]"]
    
    # Get the most common tokens and add special tokens
    vocab = special_tokens + sorted(token_counts.keys())

    # Ensure output directory exists
    output_dir = os.path.dirname(output_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Write to the output file
    with open(output_path, "w", encoding="utf-8") as f:
        for token in vocab:
            f.write(token + "\n")

    print(f"Vocabulary created successfully at: {output_path}")
    print(f"Total unique tokens found: {len(token_counts)}")
    print(f"Total tokens in vocab (including special tokens): {len(vocab)}")


if __name__ == "__main__":
    # Define the paths
    train_file_path = "datasets/my_fin_apps/train_dataset.tsv"
    output_vocab_path = "models/custom_vocab.txt"
    
    # Run the vocabulary creation
    create_vocab_from_tsv(train_file_path, output_vocab_path) 